import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  Switch,
  Platform,
  Alert,
  TouchableOpacity,
  Image,
  Modal,
  ActivityIndicator,
} from 'react-native';
import AnimalSearchDropdown from '@/components/AnimalSearchDropdown';
import SpeciesFilterRow from '@/components/SpeciesFilterRow';
import { useRouter, useLocalSearchParams, Stack } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { colors } from '@/constants/colors';
import { useAnimalStore } from '@/store/animal-store';
import { useHealthCheckStore } from '@/store/health-check-store';
import { updateAnimalHealthStatus } from '@/utils/healthStatus';
import { useSettingsStore } from '@/store/settings-store';
import { useAuthStore } from '@/store/auth-store';
import { useTranslation } from '@/hooks/useTranslation';
import * as ImagePicker from 'expo-image-picker';
import {
  ChevronDown,
  Save,
  Thermometer,
  Scale,
  Apple,
  Droplets,
  Wind,
  Footprints,
  CircleDot,
  Shirt,
  Eye,
  Ear,
  AlertTriangle,
  Calendar,
  FileText,
  Check,
  X,
  Camera,
  Image as ImageIcon,
  Bird,
  Cat,
  Fish,
  Rat,
  Rabbit,
  Dog,
  ArrowLeft,
  Mic,
  MicOff,
  Stethoscope,
} from 'lucide-react-native';
import { HealthCheck } from '@/types/healthCheck';
import DatePickerInput from '@/components/DatePickerInput';
import { LinearGradient } from 'expo-linear-gradient';
import { analyzeAnimalHealthImage } from '@/services/openai-service';
import { breedsBySpecies } from '@/constants/breeds';
import { startSpeechRecognition, stopSpeechRecognition, setCurrentField } from '@/services/speech-service';
import { useAudioFeedback } from '@/hooks/useAudioFeedback';
import { getStoredLanguage, getSpeechLanguageCode } from '@/services/language-service';
import { useThemeColors } from '@/hooks/useThemeColors'; // Import the theme hook
import Button from '@/components/Button';
import ImageCaptureButtons from '@/components/ImageCaptureButtons';
// Define types for dropdown options
interface DropdownOption {
  value: string;
  label: string;
  icon: React.ReactNode;
}

// Define types for the IconDropdown component
interface IconDropdownProps {
  label: string;
  icon: React.ReactNode;
  options: DropdownOption[];
  selectedValue: string;
  onValueChange: (value: string) => void;
  colorMapping?: Record<string, string>;
}

// Define types for the AnimalCategorySelector component
interface AnimalCategoryProps {
  categories: Array<{
    value: string;
    label: string;
    icon: React.ReactNode;
  }>;
  selectedCategory: string;
  onSelectCategory: (category: string) => void;
}

// Animal category icons
const animalIcons = {
  Cow: <Cat size={24} color={colors.primary} />,
  Dog: <Dog size={24} color={colors.primary} />,
  Cat: <Cat size={24} color={colors.primary} />,
  Goat: <Rabbit size={24} color={colors.primary} />,
  Pig: <Rat size={24} color={colors.primary} />,
  Poultry: <Bird size={24} color={colors.primary} />,
  Fish: <Fish size={24} color={colors.primary} />
};

// Health parameter icons
const healthIcons = {
  temperature: <Thermometer size={24} color={colors.primary} />,
  weight: <Scale size={24} color={colors.primary} />,
  appetite: <Apple size={24} color={colors.primary} />,
  hydration: <Droplets size={24} color={colors.primary} />,
  respiration: <Wind size={24} color={colors.primary} />,
  gait: <Footprints size={24} color={colors.primary} />,
  fecal: <CircleDot size={24} color={colors.primary} />,
  coat: <Shirt size={24} color={colors.primary} />,
  eyes: <Eye size={24} color={colors.primary} />,
  ears: <Ear size={24} color={colors.primary} />
};

// Status colors
const statusColors = {
  normal: colors.success,
  warning: colors.warning,
  severe: colors.error
};

// Enhanced dropdown component with icons and visual cues
const IconDropdown: React.FC<IconDropdownProps> = ({
  label,
  icon,
  options,
  selectedValue,
  onValueChange,
  colorMapping = {}
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const { t, language } = useTranslation();
    const themedColors = useThemeColors(); // Use the theme hook
  const selectedOption = options.find(option => option.value === selectedValue);
  const selectedColor = colorMapping[selectedValue] || colors.text;
  const styles = getStyles(themedColors, language);
  return (
    <View style={styles.dropdownContainer}>
      <View style={styles.labelContainer}>
        {icon}
        <Text style={styles.label}>{label}</Text>
      </View>

      <TouchableOpacity
        style={[styles.dropdownButton, { borderColor: selectedColor }]}
        onPress={() => setIsOpen(true)}
      >
        {selectedOption?.icon && (
          <View style={[styles.statusIcon, { backgroundColor: selectedColor }]}>
            {selectedOption.icon}
          </View>
        )}
        <Text style={[styles.dropdownButtonText, { color: selectedColor }]}>
          {selectedOption?.label || 'Select an option'}
        </Text>
        <ChevronDown size={20} color={selectedColor} />
      </TouchableOpacity>

      <Modal
        visible={isOpen}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setIsOpen(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setIsOpen(false)}
        >
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>{label}</Text>

            <View style={styles.optionsList}>
              {options.map(option => {
                const optionColor = colorMapping[option.value] || colors.text;

                return (
                  <TouchableOpacity
                    key={option.value}
                    style={[
                      styles.optionItem,
                      selectedValue === option.value && styles.selectedOption
                    ]}
                    onPress={() => {
                      onValueChange(option.value);
                      setIsOpen(false);
                    }}
                  >
                    {option.icon && (
                      <View style={[styles.optionIcon, { backgroundColor: optionColor }]}>
                        {option.icon}
                      </View>
                    )}
                    <Text style={[
                      styles.optionText,
                      { color: optionColor },
                      selectedValue === option.value && styles.selectedOptionText
                    ]}>
                      {option.label}
                    </Text>
                    {selectedValue === option.value && (
                      <Check size={20} color={colors.primary} />
                    )}
                  </TouchableOpacity>
                );
              })}
            </View>

            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setIsOpen(false)}
            >
              <Text style={styles.closeButtonText}>{t('common.close')}</Text>
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

// Animal category selector
const AnimalCategorySelector: React.FC<AnimalCategoryProps> = ({
  categories,
  selectedCategory,
  onSelectCategory
}) => {
  const themedColors = useThemeColors(); // Use the theme hook
  const { language } = useTranslation();
  const styles = getStyles(themedColors, language);
  return (
    <View style={styles.categoryContainer}>
      <Text style={styles.categoryTitle}>Animal Type</Text>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.categoryList}
      >
        {categories.map(category => (
          <TouchableOpacity
            key={category.value}
            style={[
              styles.categoryItem,
              selectedCategory === category.value && styles.selectedCategoryItem
            ]}
            onPress={() => onSelectCategory(category.value)}
          >
            <View style={[
              styles.categoryIconContainer,
              selectedCategory === category.value && styles.selectedCategoryIconContainer
            ]}>
              {category.icon}
            </View>
            <Text style={[
              styles.categoryText,
              selectedCategory === category.value && styles.selectedCategoryText
            ]}>
              {category.label}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
};

// Image capture component
const ImageCaptureSection = ({
  onImageCaptured,
  onAnalysisComplete,
  imageUri,
  isAnalyzing,
  analysisError
}: {
  onImageCaptured: (uri: string) => void;
  onAnalysisComplete: (results: any) => void;
  imageUri: string | null;
  isAnalyzing: boolean;
  analysisError: string | null;
}) => {
  const { t } = useTranslation(); // Call useTranslation inside the component
  const { openaiApiKey } = useSettingsStore();
  const themedColors = useThemeColors(); // Use the theme hook
  const { language } = useTranslation();
  const styles = getStyles(themedColors, language);

  const takePicture = async () => {
    // Skip camera on web platform
    if (Platform.OS === 'web') {
      Alert.alert('Not Available', 'Camera is not available on web. Please use the Select Image option instead.');
      return;
    }

    const { status } = await ImagePicker.requestCameraPermissionsAsync();

    if (status !== 'granted') {
      Alert.alert('Permission required', 'Camera permission is required to take pictures');
      return;
    }

    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const uri = result.assets[0].uri;
        onImageCaptured(uri);

        if (openaiApiKey) {
          try {
            const analysisResults = await analyzeAnimalHealthImage(uri, openaiApiKey);
            onAnalysisComplete(analysisResults);
          } catch (error) {
            console.error('Error analyzing image:', error);
            Alert.alert('Analysis Error', 'Failed to analyze the image. Please try again or fill the form manually.');
          }
        } else {
          Alert.alert('API Key Missing', 'Please add your OpenAI API key in settings to use image analysis.');
        }
      }
    } catch (error) {
      console.error('Error taking picture:', error);
      Alert.alert('Error', 'Failed to take picture. Please try again.');
    }
  };

  const pickImage = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permission required', 'Media library permission is required to select images');
      return;
    }

    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const uri = result.assets[0].uri;
        onImageCaptured(uri);

        if (openaiApiKey) {
          try {
            const analysisResults = await analyzeAnimalHealthImage(uri, openaiApiKey);
            onAnalysisComplete(analysisResults);
          } catch (error) {
            console.error('Error analyzing image:', error);
            Alert.alert('Analysis Error', 'Failed to analyze the image. Please try again or fill the form manually.');
          }
        } else {
          Alert.alert('API Key Missing', 'Please add your OpenAI API key in settings to use image analysis.');
        }
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to select image. Please try again.');
    }
  };

  return (
    <View style={styles.imageCaptureContainer}>
      <Text style={styles.imageCaptureTitle}>{t('dashboard.CaptureorSelectImageforAnalysis')}</Text>
      <Text style={styles.imageCaptureSubtitle}>{t('dashboard.Takeaphotooftheanimalscondition')}</Text>
      <ImageCaptureButtons
        onTakePhoto={takePicture}
        onChooseFromLibrary={pickImage}
        disabled={isAnalyzing}
      />

      {imageUri ? (
        <View style={styles.capturedImageContainer}>
          <Image
            source={{ uri: imageUri }}
            style={styles.capturedImage}
            resizeMode="cover"
          />
          {isAnalyzing && (
            <View style={styles.analysisOverlay}>
              <ActivityIndicator size="large" color={themedColors.primary} />
            </View>
          )}
          {analysisError && (
            <View style={styles.errorOverlay}>
              <AlertTriangle size={24} color="white" />
              <Text style={styles.errorText}>{analysisError}</Text>
              <TouchableOpacity
                style={styles.retryButton}
                onPress={() => {
                  if (openaiApiKey && imageUri) {
                    onImageCaptured(imageUri);
                  }
                }}
              >
                <Text style={styles.retryButtonText}>{t('animals.retryAnalysis')}</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      ) : null}
    </View>
  );
};



export default function AddHealthCheckScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const animalId = params.animalId as string;
  const fromDashboard = params.fromDashboard === 'true';

  const { user } = useAuthStore();
  const { animals, fetchAnimals, updateAnimal } = useAnimalStore();
  const { addHealthCheck } = useHealthCheckStore();
  const { openaiApiKey } = useSettingsStore();
  const { t, language } = useTranslation();
  const { playFeedback } = useAudioFeedback();
  const themedColors = useThemeColors(); // Use the theme hook
  const styles = getStyles(themedColors, language);

  const [selectedAnimalId, setSelectedAnimalId] = useState(animalId || '');
  const [temperature, setTemperature] = useState('');
  const [weight, setWeight] = useState('');
  const [appetite, setAppetite] = useState('normal');
  const [hydration, setHydration] = useState('normal');
  const [respiration, setRespiration] = useState('normal');
  const [gait, setGait] = useState('normal');
  const [fecal, setFecal] = useState('normal');
  const [coat, setCoat] = useState('normal');
  const [eyes, setEyes] = useState('normal');
  const [ears, setEars] = useState('normal');
  const [notes, setNotes] = useState('');
  const [abnormalities, setAbnormalities] = useState(false);
  const [date, setDate] = useState(new Date());
  // Always set next check date to exactly 7 days from current date
  const [nextCheckDate, setNextCheckDate] = useState(new Date(Date.now() + 7 * 24 * 60 * 60 * 1000));

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedSpecies, setSelectedSpecies] = useState<string | null>(null);
  const [showSpeciesFilter, setShowSpeciesFilter] = useState(fromDashboard);

  // Image capture and analysis states
  const [capturedImageUri, setCapturedImageUri] = useState<string | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisError, setAnalysisError] = useState<string | null>(null);

  // Voice recording states
  const [isRecordingNotes, setIsRecordingNotes] = useState(false);
  const [isProcessingSpeech, setIsProcessingSpeech] = useState(false);

  // Get species from breedsBySpecies
  const speciesList = Object.keys(breedsBySpecies);

  // Updated animal categories with proper icons
  const animalCategories = speciesList.map(species => ({
    value: species,
    label: species,
    icon: animalIcons[species as keyof typeof animalIcons] || <Cat size={24} color={colors.primary} />
  }));

  // Filter animals by selected species if a filter is active
  const filteredAnimals = selectedSpecies
    ? animals.filter(animal => animal.species === selectedSpecies)
    : animals;

  useEffect(() => {
    // Check for abnormalities based on selected values
    const hasAbnormalities =
      appetite !== 'normal' ||
      hydration !== 'normal' ||
      respiration !== 'normal' ||
      gait !== 'normal' ||
      fecal !== 'normal' ||
      coat !== 'normal' ||
      eyes !== 'normal' ||
      ears !== 'normal';

    setAbnormalities(hasAbnormalities);
  }, [appetite, hydration, respiration, gait, fecal, coat, eyes, ears]);

  // Fetch animals if needed
  useEffect(() => {
    if (animals.length === 0 && user) {
      fetchAnimals(user.id);
    }
  }, [animals.length, fetchAnimals, user]);

  const resetForm = () => {
    setSelectedAnimalId('');
    setTemperature('');
    setWeight('');
    setAppetite('normal');
    setHydration('normal');
    setRespiration('normal');
    setGait('normal');
    setFecal('normal');
    setCoat('normal');
    setEyes('normal');
    setEars('normal');
    setNotes('');
    setAbnormalities(false);
    setDate(new Date());
    // Set next check date to exactly 7 days from current date
    const newDate = new Date();
    newDate.setDate(newDate.getDate() + 7);
    setNextCheckDate(newDate);
    setCapturedImageUri(null);
    setAnalysisError(null);
  };

  const handleSubmit = async () => {
    if (!selectedAnimalId) {
      Alert.alert('Error', 'Please select an animal');
      return;
    }

    setIsSubmitting(true);

    // Create a clean object without undefined values for Firestore
    const healthCheckData: Partial<HealthCheck> = {
      animalId: selectedAnimalId,
      date: date.getTime(),
      appetite: appetite as 'normal' | 'increased' | 'decreased' | 'none',
      hydration: hydration as 'normal' | 'dehydrated' | 'overhydrated',
      respiration: respiration as 'normal' | 'increased' | 'decreased' | 'labored',
      gait: gait as 'normal' | 'limping' | 'stiff' | 'unable',
      fecal: fecal as 'normal' | 'diarrhea' | 'constipated' | 'bloody',
      coat: coat as 'normal' | 'dull' | 'patchy' | 'irritated',
      eyes: eyes as 'normal' | 'discharge' | 'cloudy' | 'red',
      ears: ears as 'normal' | 'discharge' | 'red' | 'swollen',
      notes,
      abnormalities: abnormalities,
      nextCheckDate: nextCheckDate.getTime(),
    };

    // Only add temperature if it's a valid number
    if (temperature && !isNaN(parseFloat(temperature))) {
      healthCheckData.temperature = parseFloat(temperature);
    }

    // Only add weight if it's a valid number
    if (weight && !isNaN(parseFloat(weight))) {
      healthCheckData.weight = parseFloat(weight);
    }

    // Only add imageUri if it exists
    if (capturedImageUri) {
      healthCheckData.imageUri = capturedImageUri;
    }

    try {
      // Add the health check
      const newHealthCheck = await addHealthCheck(healthCheckData);

      // Get the animal
      const animal = animals.find(a => a.id === selectedAnimalId);

      if (animal) {
        // Update the animal's health status
        const healthStatusUpdates = updateAnimalHealthStatus(animal, newHealthCheck);
        await updateAnimal(selectedAnimalId, healthStatusUpdates);
      }

      // Show success alert and navigate to animal detail screen
      Alert.alert(
        'Success',
        'Health check added successfully',
        [
          {
            text: 'OK',
            onPress: () => {
              // Navigate to the animal detail screen
              router.push(`/animals/${selectedAnimalId}`);
            },
            style: 'default'
          }
        ]
      );
    } catch (error) {
      console.error('Error adding health check:', error);
      Alert.alert('Error', 'Failed to add health check');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleImageCaptured = (uri: string) => {
    setCapturedImageUri(uri);
    setIsAnalyzing(true);
    setAnalysisError(null);

    // If no API key, show alert and stop analysis
    if (!openaiApiKey) {
      setIsAnalyzing(false);
      setAnalysisError("API key missing. Please add your OpenAI API key in settings.");
      return;
    }

    // Set a timeout to prevent infinite loading
    const analysisTimeout = setTimeout(() => {
      if (isAnalyzing) {
        setIsAnalyzing(false);
        setAnalysisError("Analysis timed out. Please try again.");
      }
    }, 30000); // 30 second timeout

    // Proceed with analysis
    analyzeAnimalHealthImage(uri, openaiApiKey)
      .then(results => {
        clearTimeout(analysisTimeout);
        handleAnalysisComplete(results);
      })
      .catch(error => {
        clearTimeout(analysisTimeout);
        console.error('Error analyzing image:', error);
        setIsAnalyzing(false);
        setAnalysisError("Failed to analyze image. Please try again or fill the form manually.");
      });
  };

  const handleAnalysisComplete = (results: any) => {
    setIsAnalyzing(false);
    setAnalysisError(null);

    if (!results) return;

    console.log("Analysis results:", JSON.stringify(results));

    // Update form fields based on analysis results
    if (results.temperature) setTemperature(results.temperature.toString());
    if (results.weight) setWeight(results.weight.toString());
    if (results.appetite) setAppetite(results.appetite);
    if (results.hydration) setHydration(results.hydration);
    if (results.respiration) setRespiration(results.respiration);
    if (results.gait) setGait(results.gait);
    if (results.fecal) setFecal(results.fecal);
    if (results.coat) setCoat(results.coat);
    if (results.eyes) setEyes(results.eyes);
    if (results.ears) setEars(results.ears);

    // Set notes from analysis
    if (results.analysis) {
      setNotes(results.analysis);
    }

    // Always set next check date to 7 days from current date
    const newNextCheckDate = new Date();
    newNextCheckDate.setDate(newNextCheckDate.getDate() + 7);
    setNextCheckDate(newNextCheckDate);

    // Show success message
    Alert.alert(
      'Analysis Complete',
      'The image has been analyzed and the form has been updated with the findings. Please review and adjust if needed.',
      [{ text: 'OK' }]
    );
  };

  // Find selected animal for reference
  const selectedAnimal = animals.find(animal => animal.id === selectedAnimalId);

  // Options for dropdowns with icons and colors
  const appetiteOptions = [
    { value: 'normal', label: t('healthChecks.normal'), icon: <Check size={16} color="white" /> },
    { value: 'increased', label: t('healthChecks.increased'), icon: <ChevronDown size={16} color="white" /> },
    { value: 'decreased', label: t('healthChecks.decreased'), icon: <ChevronDown size={16} color="white" /> },
    { value: 'none', label: t('healthChecks.none'), icon: <X size={16} color="white" /> }
  ];

  const hydrationOptions = [
    { value: 'normal', label: t('healthChecks.normal'), icon: <Check size={16} color="white" /> },
    { value: 'dehydrated', label: t('healthChecks.dehydrated'), icon: <AlertTriangle size={16} color="white" /> },
    { value: 'overhydrated', label: t('healthChecks.overhydrated'), icon: <AlertTriangle size={16} color="white" /> }
  ];

  const respirationOptions = [
    { value: 'normal', label: t('healthChecks.normal'), icon: <Check size={16} color="white" /> },
    { value: 'increased', label: t('healthChecks.increased'), icon: <ChevronDown size={16} color="white" /> },
    { value: 'decreased', label: t('healthChecks.decreased'), icon: <ChevronDown size={16} color="white" /> },
    { value: 'labored', label: t('healthChecks.labored'), icon: <AlertTriangle size={16} color="white" /> }
  ];

  const gaitOptions = [
    { value: 'normal', label: t('healthChecks.normal'), icon: <Check size={16} color="white" /> },
    { value: 'limping', label: t('healthChecks.limping'), icon: <AlertTriangle size={16} color="white" /> },
    { value: 'stiff', label: t('healthChecks.stiff'), icon: <AlertTriangle size={16} color="white" /> },
    { value: 'unable', label: t('healthChecks.unableToMove'), icon: <X size={16} color="white" /> }
  ];

  const fecalOptions = [
    { value: 'normal', label: t('healthChecks.normal'), icon: <Check size={16} color="white" /> },
    { value: 'diarrhea', label: t('healthChecks.diarrhea'), icon: <AlertTriangle size={16} color="white" /> },
    { value: 'constipated', label: t('healthChecks.constipated'), icon: <AlertTriangle size={16} color="white" /> },
    { value: 'bloody', label: t('healthChecks.bloody'), icon: <X size={16} color="white" /> }
  ];

  const coatOptions = [
    { value: 'normal', label: t('healthChecks.normal'), icon: <Check size={16} color="white" /> },
    { value: 'dull', label: t('healthChecks.dull'), icon: <AlertTriangle size={16} color="white" /> },
    { value: 'patchy', label: t('healthChecks.patchy'), icon: <AlertTriangle size={16} color="white" /> },
    { value: 'irritated', label: t('healthChecks.irritated'), icon: <X size={16} color="white" /> }
  ];

  const eyesOptions = [
    { value: 'normal', label: t('healthChecks.normal'), icon: <Check size={16} color="white" /> },
    { value: 'discharge', label: t('healthChecks.discharge'), icon: <AlertTriangle size={16} color="white" /> },
    { value: 'cloudy', label: t('healthChecks.cloudy'), icon: <AlertTriangle size={16} color="white" /> },
    { value: 'red', label: t('healthChecks.red'), icon: <X size={16} color="white" /> }
  ];

  const earsOptions = [
    { value: 'normal', label: t('healthChecks.normal'), icon: <Check size={16} color="white" /> },
    { value: 'discharge', label: t('healthChecks.discharge'), icon: <AlertTriangle size={16} color="white" /> },
    { value: 'red', label: t('healthChecks.red'), icon: <AlertTriangle size={16} color="white" /> },
    { value: 'swollen', label: t('healthChecks.swollen'), icon: <X size={16} color="white" /> }
  ];

  // Color mapping for status
  const statusColorMapping: Record<string, string> = {
    'normal': themedColors.success,
    'increased': themedColors.warning,
    'decreased': themedColors.warning,
    'none': themedColors.error,
    'dehydrated': themedColors.warning,
    'overhydrated': themedColors.warning,
    'labored': themedColors.error,
    'limping': themedColors.warning,
    'stiff': themedColors.warning,
    'unable': themedColors.error,
    'diarrhea': themedColors.warning,
    'constipated': themedColors.warning,
    'bloody': themedColors.error,
    'dull': themedColors.warning,
    'patchy': themedColors.warning,
    'irritated': themedColors.error,
    'discharge': themedColors.warning,
    'cloudy': themedColors.warning,
    'red': themedColors.error,
    'swollen': themedColors.error
  };

  // Function to start voice recording for notes
  const startRecording = async () => {
    // Show unsupported message on web platform
    if (Platform.OS === 'web') {
      Alert.alert(
        t('common.unsupported'),
        t('common.voiceRecordingWebUnsupported')
      );
      return;
    }

    try {
      setIsRecordingNotes(true);
      setIsProcessingSpeech(true);
      
      try {
        // Get the current app language
        const appLanguage = await getStoredLanguage();
        // Get the appropriate speech language code (ur-PK for Urdu)
        const speechLanguage = getSpeechLanguageCode(appLanguage);
        
        // Set the current field in the speech service
        setCurrentField('notes');
        
        // Start recording with the correct language
        await startSpeechRecognition({ language: speechLanguage });
        
        // Play feedback
        playFeedback('toggle');
      } catch (error) {
        console.error('Error starting speech recognition:', error);
        Alert.alert(t('common.error'), t('common.speechRecognitionError'));
        setIsProcessingSpeech(false);
        setIsRecordingNotes(false);
        setCurrentField(null);
      }
    } catch (error) {
      console.error('Error starting recording:', error);
      setIsProcessingSpeech(false);
      setIsRecordingNotes(false);
    }
  };

  // Function to stop voice recording
  const stopRecording = async () => {
    try {
      setIsProcessingSpeech(true);
      
      // Stop recording and get the transcribed text
      const transcribedText = await stopSpeechRecognition();
      
      // Append the transcribed text to the existing notes
      if (transcribedText) {
        setNotes(prevNotes => {
          if (prevNotes.trim()) {
            return `${prevNotes}\n${transcribedText}`;
          }
          return transcribedText;
        });
      }
      
      // Play feedback
      playFeedback('success');
    } catch (error) {
      console.error('Error stopping recording:', error);
      Alert.alert(t('common.error'), t('common.speechRecognitionError'));
      playFeedback('error');
    } finally {
      setIsRecordingNotes(false);
      setIsProcessingSpeech(false);
      setCurrentField(null);
    }
  };

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen options={{
        title: t('dashboard.healthChecks'),
        headerTitleStyle: { fontWeight: 'bold', color: themedColors.text },
        headerStyle: {
          backgroundColor: themedColors.background,
        },
        headerTintColor: themedColors.text,
        headerLeft: () => (
          <TouchableOpacity onPress={() => router.back()} style={{ marginLeft: 8 }}>
            <ArrowLeft size={24} color={themedColors.text} />
          </TouchableOpacity>
        )
      }} />

      <ScrollView style={styles.scrollView}>
        <View style={styles.formContainer}>
          {/* Perform Health Check Header */}
          <View style={styles.healthCheckHeader}>
            <View style={styles.healthCheckIconContainer}>
              <Stethoscope size={32} color="white" />
            </View>
            <Text style={styles.healthCheckHeaderText}>Perform Health Check</Text>
            <Text style={styles.healthCheckSubtext}>Monitor your animal's health and wellbeing</Text>
          </View>

          {/* Image Capture Section */}
          <ImageCaptureSection
            onImageCaptured={handleImageCaptured}
            onAnalysisComplete={handleAnalysisComplete}
            imageUri={capturedImageUri}
            isAnalyzing={isAnalyzing}
            analysisError={analysisError}
          />

          {/* Species Filter - Only shown when accessed from dashboard */}
          {showSpeciesFilter && (
            <View style={styles.formGroup}>
              <View style={styles.labelContainer}>
                <Text style={styles.label}>{t('animals.type')}</Text>
              </View>
              <SpeciesFilterRow
                selectedSpecies={selectedSpecies}
                onSelectSpecies={setSelectedSpecies}
              />
            </View>
          )}

          {/* Animal Selection */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Text style={[styles.label, language === 'ur' ? styles.urduText : null]}>{t('animals.selectAnimal')}</Text>
            </View>

            <AnimalSearchDropdown
              placeholder={t('animals.selectAnimal')}
              animals={filteredAnimals}
              value={selectedAnimalId}
              onSelect={setSelectedAnimalId}
            />
          </View>

          {/* Date */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Calendar size={24} color={themedColors.primary} />
              <Text style={styles.label}>{t('records.date')}</Text>
            </View>
            <DatePickerInput
              label=""
              value={date}
              onChange={setDate}
            />
          </View>

          {/* Temperature */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Thermometer size={24} color={themedColors.primary} />
              <Text style={[styles.label, language === 'ur' ? styles.urduText : null]}>{t('healthChecks.temperature')}</Text>
            </View>
            <TextInput
              style={styles.input}
              value={temperature}
              onChangeText={setTemperature}
              keyboardType="numeric"
              placeholder={t('healthChecks.enterTemperature')}
              placeholderTextColor={themedColors.textSecondary}
            />
          </View>

          {/* Weight */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Scale size={24} color={themedColors.primary} />
              <Text style={[styles.label, language === 'ur' ? styles.urduText : null]}>{t('healthChecks.weight')}</Text>
            </View>
            <TextInput
              style={styles.input}
              value={weight}
              onChangeText={setWeight}
              keyboardType="numeric"
              placeholder={t('healthChecks.enterWeight')}
              placeholderTextColor={themedColors.textSecondary}
            />
          </View>

          {/* Appetite */}
          <IconDropdown
            label={t('healthChecks.appetite')}
            icon={healthIcons.appetite}
            options={appetiteOptions}
            selectedValue={appetite}
            onValueChange={setAppetite}
            colorMapping={statusColorMapping}
          />

          {/* Hydration */}
          <IconDropdown
            label={t('healthChecks.hydration')}
            icon={healthIcons.hydration}
            options={hydrationOptions}
            selectedValue={hydration}
            onValueChange={setHydration}
            colorMapping={statusColorMapping}
          />

          {/* Respiration */}
          <IconDropdown
            label={t('healthChecks.respiration')}
            icon={healthIcons.respiration}
            options={respirationOptions}
            selectedValue={respiration}
            onValueChange={setRespiration}
            colorMapping={statusColorMapping}
          />

          {/* Gait */}
          <IconDropdown
            label={t('healthChecks.gait')}
            icon={healthIcons.gait}
            options={gaitOptions}
            selectedValue={gait}
            onValueChange={setGait}
            colorMapping={statusColorMapping}
          />

          {/* Fecal */}
          <IconDropdown
            label={t('healthChecks.fecal')}
            icon={healthIcons.fecal}
            options={fecalOptions}
            selectedValue={fecal}
            onValueChange={setFecal}
            colorMapping={statusColorMapping}
          />

          {/* Coat */}
          <IconDropdown
            label={t('healthChecks.coat')}
            icon={healthIcons.coat}
            options={coatOptions}
            selectedValue={coat}
            onValueChange={setCoat}
            colorMapping={statusColorMapping}
          />

          {/* Eyes */}
          <IconDropdown
            label={t('healthChecks.eyes')}
            icon={healthIcons.eyes}
            options={eyesOptions}
            selectedValue={eyes}
            onValueChange={setEyes}
            colorMapping={statusColorMapping}
          />

          {/* Ears */}
          <IconDropdown
            label={t('healthChecks.ears')}
            icon={healthIcons.ears}
            options={earsOptions}
            selectedValue={ears}
            onValueChange={setEars}
            colorMapping={statusColorMapping}
          />

          {/* Abnormalities */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <AlertTriangle size={24} color={themedColors.primary} />
              <Text style={[styles.label, language === 'ur' ? styles.urduText : null]}>{t('healthChecks.abnormalitiesDetected')}</Text>
            </View>
            <View style={styles.abnormalitiesContainer}>
              <Switch
                value={abnormalities}
                onValueChange={setAbnormalities}
                trackColor={{ false: themedColors.border, true: themedColors.primaryLight }}
                thumbColor={abnormalities ? themedColors.primary : themedColors.card}
              />
              <Text style={[
                styles.abnormalitiesText,
                { color: abnormalities ? themedColors.error : themedColors.success }
              ]}>
                {abnormalities ? t('common.yes') : t('common.no')}
              </Text>
            </View>
            {abnormalities && (
              <View style={styles.warningBox}>
                <AlertTriangle size={20} color={themedColors.error} />
                <Text style={styles.warningText}>
                  {t('healthChecks.abnormalitiesWarning')}
                </Text>
              </View>
            )}
          </View>

          {/* Next Check Date */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Calendar size={24} color={themedColors.primary} />
              <Text style={[styles.label, language === 'ur' ? styles.urduText : null]}>{t('healthChecks.nextCheckDate')}</Text>
            </View>
            <DatePickerInput
              label=""
              value={nextCheckDate}
              onChange={setNextCheckDate}
            />
          </View>

          {/* Notes */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <FileText size={24} color={themedColors.primary} />
              <Text style={[styles.label, language === 'ur' ? styles.urduText : null]}>{t('records.notes')}</Text>
            </View>
            <View style={styles.inputContainer}>
              <TextInput
                style={[styles.input, styles.textArea]}
                value={notes}
                onChangeText={setNotes}
                placeholder={t('records.addNotes')}
                placeholderTextColor={themedColors.textSecondary}
                multiline
                numberOfLines={4}
                textAlignVertical="top"
              />
              {Platform.OS !== 'web' && (
                <TouchableOpacity
                  style={[
                    styles.voiceButton,
                    isRecordingNotes && styles.voiceButtonRecording
                  ]}
                  onPress={() => {
                    if (isRecordingNotes) {
                      stopRecording();
                    } else {
                      startRecording();
                    }
                  }}
                >
                  {isRecordingNotes ? (
                    <MicOff size={20} color={themedColors.error} />
                  ) : (
                    <Mic size={20} color={themedColors.primary} />
                  )}
                </TouchableOpacity>
              )}
            </View>
            {isRecordingNotes && (
              <View style={styles.recordingIndicator}>
                <Text style={styles.recordingText}>{t('common.recording')}</Text>
              </View>
            )}
          </View>

          {/* <TouchableOpacity
            style={styles.saveButton}
            onPress={handleSubmit}
            disabled={isSubmitting}
          >
            <LinearGradient
              colors={[themedColors.primary, colors.primaryDark]}
              style={styles.saveButtonGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
            >
              <Save size={24} color="white" />
              <Text style={[styles.saveButtonText, language === 'ur' ? styles.urduText : null]}>{t('healthChecks.saveHealthCheck')}</Text>
            </LinearGradient>
          </TouchableOpacity> */}
          <View style={styles.buttonContainer}>
            <Button
              title={t('healthChecks.saveHealthCheck')}
              onPress={handleSubmit}
              isLoading={isSubmitting}
              leftIcon={<Save size={20} color="white" />}
            />
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const getStyles = (themedColors: ReturnType<typeof useThemeColors>, language: string) => StyleSheet.create({
  buttonContainer: {
    marginTop: 12, // Increased margin for the button
  },
  urduText: {
    fontFamily: 'System',
    textAlign: 'right',
  },

  container: {
    flex: 1,
    backgroundColor: themedColors.background,
  },
  scrollView: {
    flex: 1,
  },
  formContainer: {
    padding: 16,
  },
  formGroup: {
    marginBottom: 20,
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    color: themedColors.text,
  },
  input: {
    backgroundColor: themedColors.card,
    borderWidth: 1,
    borderColor: themedColors.border,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: themedColors.text,
  },
  textArea: {
    minHeight: 100,
  },
  animalSelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: themedColors.card,
    borderWidth: 1,
    borderColor: themedColors.border,
    borderRadius: 8,
    padding: 12,
  },
  animalSelectorText: {
    fontSize: 16,
    color: themedColors.text,
  },
  animalList: {
    backgroundColor: themedColors.card,
    borderWidth: 1,
    borderColor: themedColors.border,
    borderRadius: 8,
    marginTop: 8,
    maxHeight: 200,
    overflow: 'hidden',
  },
  animalItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: themedColors.border,
  },
  selectedAnimalItem: {
    backgroundColor: themedColors.primaryLight,
  },
  animalItemText: {
    fontSize: 16,
    color: themedColors.text,
  },
  selectedAnimalItemText: {
    color: themedColors.primary,
    fontWeight: 'bold',
  },
  noAnimalsMessage: {
    padding: 16,
    alignItems: 'center',
  },
  noAnimalsText: {
    color: themedColors.textSecondary,
    fontStyle: 'italic',
  },
  abnormalitiesContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  abnormalitiesText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  warningBox: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: themedColors.errorLight,
    borderWidth: 1,
    borderColor: themedColors.error,
    borderRadius: 8,
    padding: 12,
    marginTop: 8,
    gap: 8,
  },
  warningText: {
    color: themedColors.error,
    fontSize: 14,
    flex: 1,
  },
  // saveButton: {
  //   marginTop: 16,
  //   marginBottom: 32,
  //   borderRadius: 8,
  //   overflow: 'hidden',
  //   elevation: 2,
  //   shadowColor: '#000',
  //   shadowOffset: { width: 0, height: 2 },
  //   shadowOpacity: 0.1,
  //   shadowRadius: 4,
  // },
  // saveButtonGradient: {
  //   flexDirection: 'row',
  //   alignItems: 'center',
  //   justifyContent: 'center',
  //   paddingVertical: 16,
  //   gap: 8,
  // },
  // saveButtonText: {
  //   color: 'white',
  //   fontSize: 18,
  //   fontWeight: 'bold',
  // },
  dropdownContainer: {
    marginBottom: 20,
  },
  dropdownButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: themedColors.card,
    borderWidth: 2,
    borderRadius: 8,
    padding: 12,
  },
  dropdownButtonText: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
  },
  statusIcon: {
    width: 28,
    height: 28,
    borderRadius: 14,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: themedColors.card,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    padding: 16,
    maxHeight: '80%',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: themedColors.text,
    marginBottom: 16,
    textAlign: 'center',
  },
  optionsList: {
    marginBottom: 16,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: themedColors.border,
  },
  selectedOption: {
    backgroundColor: themedColors.primaryLight,
  },
  optionIcon: {
    width: 28,
    height: 28,
    borderRadius: 14,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  optionText: {
    flex: 1,
    fontSize: 16,
  },
  selectedOptionText: {
    fontWeight: 'bold',
  },
  closeButton: {
    backgroundColor: themedColors.border,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: themedColors.text,
  },
  categoryContainer: {
    marginBottom: 20,
  },
  categoryTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: themedColors.text,
    marginBottom: 8,
  },
  categoryList: {
    flexDirection: 'row',
    paddingVertical: 8,
  },
  categoryItem: {
    alignItems: 'center',
    marginRight: 16,
    width: 80,
  },
  categoryIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: themedColors.card,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
    borderWidth: 2,
    borderColor: themedColors.border,
  },
  selectedCategoryIconContainer: {
    borderColor: themedColors.primary,
    backgroundColor: themedColors.primaryLight,
  },
  selectedCategoryItem: {
    opacity: 1,
  },
  categoryText: {
    fontSize: 14,
    color: themedColors.text,
    textAlign: 'center',
  },
  selectedCategoryText: {
    fontWeight: 'bold',
    color: themedColors.primary,
  },
  imageCaptureContainer: {
    marginBottom: 24,
    backgroundColor: themedColors.card,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: themedColors.border,
  },
  imageCaptureTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: themedColors.text,
    marginBottom: 8,
  },
  imageCaptureSubtitle: {
    fontSize: 14,
    color: themedColors.textSecondary,
    marginBottom: 16,
  },

  capturedImageContainer: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    overflow: 'hidden',
    position: 'relative',
  },
  capturedImage: {
    width: '100%',
    height: '100%',
  },
  analysisOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    alignItems: 'center',
    justifyContent: 'center',
  },

  errorOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: themedColors.isDarkMode ? 'rgba(248, 113, 113, 0.7)' : 'rgba(220, 38, 38, 0.7)',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  errorText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 8,
    marginBottom: 16,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: 'white',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  retryButtonText: {
    color: themedColors.error,
    fontWeight: 'bold',
  },
  inputContainer: {
    position: 'relative',
    width: '100%',
  },
  voiceButton: {
    position: 'absolute',
    right: 10,
    top: 10,
    padding: 8,
    borderRadius: 20,
    backgroundColor: themedColors.border,
    zIndex: 1,
  },
  voiceButtonRecording: {
    backgroundColor: themedColors.error,
  },
  recordingIndicator: {
    marginTop: 5,
    padding: 5,
    backgroundColor: themedColors.error,
    borderRadius: 4,
    alignItems: 'center',
  },
  recordingText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  healthCheckHeader: {
    backgroundColor: themedColors.primary,
    borderRadius: 16,
    padding: 20,
    marginBottom: 24,
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  healthCheckIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  healthCheckHeaderText: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  healthCheckSubtext: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
  },
});
