import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Farm, FarmStatus } from '@/types/farm';
import { collection, addDoc, updateDoc, deleteDoc, getDocs, doc, query, where, getDoc } from 'firebase/firestore';
import { firestore } from '@/config/firebase';
import { useLookupStore } from './lookup-store';

interface FarmState {
  farms: Farm[];
  selectedFarmId: string | null;
  isLoading: boolean;
  error: string | null;
}

interface FarmStore extends FarmState {
  fetchFarms: (userId: string) => Promise<Farm[]>;
  getFarm: (id: string) => Farm | undefined;
  getSelectedFarm: () => Farm | undefined;
  setSelectedFarm: (farmId: string | null) => void;
  refreshFarm: (id: string) => Promise<Farm | undefined>;
  addFarm: (farm: Omit<Farm, 'id' | 'createdAt' | 'updatedAt'>) => Promise<Farm>;
  updateFarm: (id: string, updates: Partial<Farm>) => Promise<Farm>;
  deleteFarm: (id: string) => Promise<void>;
  getFarmsByStatus: (status: FarmStatus) => Farm[];
  clearFarms: () => void;
}

export const useFarmStore = create<FarmStore>()(
  persist(
    (set, get) => ({
      farms: [],
      selectedFarmId: null,
      isLoading: false,
      error: null,

      fetchFarms: async (userId: string) => {
        set({ isLoading: true, error: null });
        try {
          // First, get the user's data to determine their role and assigned farms
          const userRef = doc(firestore, 'users', userId);
          const userDoc = await getDoc(userRef);

          if (!userDoc.exists()) {
            throw new Error('User not found');
          }

          const userData = userDoc.data();
          const userRole = userData.role;
          let fetchedFarms: Farm[] = [];

          // Get assigned farm IDs for all user roles
          const assignedFarmIds = userData.assignedFarmIds || [];

          if (userRole === 'owner') {
            // For owners, if no assignedFarmIds, fall back to querying by ownerId for backward compatibility
            if (assignedFarmIds.length > 0) {
              // Use assignedFarmIds if available
              const farmPromises = assignedFarmIds.map(async (farmId: string) => {
                const farmRef = doc(firestore, 'farms', farmId);
                const farmDoc = await getDoc(farmRef);
                if (farmDoc.exists()) {
                  return { id: farmDoc.id, ...farmDoc.data() } as Farm;
                }
                return null;
              });

              const farmResults = await Promise.all(farmPromises);
              fetchedFarms = farmResults.filter(farm => farm !== null) as Farm[];
            } else {
              // Fallback: fetch farms they own by ownerId
              const farmsRef = collection(firestore, 'farms');
              const q = query(farmsRef, where("ownerId", "==", userId));
              const querySnapshot = await getDocs(q);

              querySnapshot.forEach((doc) => {
                fetchedFarms.push({ id: doc.id, ...doc.data() } as Farm);
              });
            }
          } else if (userRole === 'admin' || userRole === 'caretaker') {
            // For employees, use assignedFarmIds
            if (assignedFarmIds.length > 0) {
              // Fetch each assigned farm
              const farmPromises = assignedFarmIds.map(async (farmId: string) => {
                const farmRef = doc(firestore, 'farms', farmId);
                const farmDoc = await getDoc(farmRef);
                if (farmDoc.exists()) {
                  return { id: farmDoc.id, ...farmDoc.data() } as Farm;
                }
                return null;
              });

              const farmResults = await Promise.all(farmPromises);
              fetchedFarms = farmResults.filter(farm => farm !== null) as Farm[];
            }
          }

          // Auto-select first farm if no farm is currently selected
          const currentState = get();
          const newState: any = { farms: fetchedFarms, isLoading: false };

          if (!currentState.selectedFarmId && fetchedFarms.length > 0) {
            newState.selectedFarmId = fetchedFarms[0].id;
          }

          set(newState);
          return fetchedFarms;
        } catch (error) {
          console.error('Error fetching farms:', error);
          set({
            error: error instanceof Error ? error.message : 'Failed to fetch farms',
            isLoading: false
          });
          return [];
        }
      },

      getFarm: (id: string) => {
        return get().farms.find(farm => farm.id === id);
      },

      getSelectedFarm: () => {
        const { selectedFarmId, farms } = get();
        if (!selectedFarmId) {
          // If no farm is explicitly selected but farms are available, return the first farm
          if (farms && farms.length > 0) {
            console.log('No farm selected, returning first farm as default:', farms[0].name);
            return farms[0];
          }
          return undefined;
        }
        return farms.find(farm => farm.id === selectedFarmId);
      },

      setSelectedFarm: (farmId: string | null) => {
        set({ selectedFarmId: farmId });
      },

      refreshFarm: async (id: string) => {
        try {
          // Get the farm directly from Firestore to ensure we have the latest data
          const farmRef = doc(firestore, 'farms', id);
          const farmDoc = await getDoc(farmRef);

          if (farmDoc.exists()) {
            const farmData = { id: farmDoc.id, ...farmDoc.data() } as Farm;

            // Update the farm in the local state
            set(state => ({
              farms: state.farms.map(farm => farm.id === id ? farmData : farm)
            }));

            return farmData;
          }
          return undefined;
        } catch (error) {
          console.error('Error refreshing farm:', error);
          return get().getFarm(id);
        }
      },

      addFarm: async (farmData) => {
        set({ isLoading: true, error: null });
        try {
          // Create formatted date strings as per the new requirement
          const now = new Date();
          const formattedDate = now.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            timeZoneName: 'short'
          });

          const newFarmData = {
            ...farmData,
            createdAt: formattedDate,
            updatedAt: formattedDate,
          };


          // Add to Firestore
          const docRef = await addDoc(collection(firestore, 'farms'), newFarmData);

          // Create the complete farm object with the Firestore ID
          const newFarm: Farm = {
            id: docRef.id,
            ...newFarmData,
          } as Farm;

          // Update local state
          set(state => ({
            farms: [...state.farms, newFarm],
            isLoading: false,
          }));

          return newFarm;
        } catch (error) {
          console.error('Error adding farm:', error);
          set({
            error: error instanceof Error ? error.message : 'Failed to add farm',
            isLoading: false
          });
          throw error;
        }
      },

      updateFarm: async (id, updates) => {
        set({ isLoading: true, error: null });
        try {
          const farm = get().getFarm(id);
          if (!farm) {
            throw new Error('Farm not found');
          }

          // Create formatted date string for update
          const now = new Date();
          const formattedDate = now.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            timeZoneName: 'short'
          });

          const updatedData = {
            ...updates,
            updatedAt: formattedDate,
          };

          // Update in Firestore
          const farmRef = doc(firestore, 'farms', id);
          await updateDoc(farmRef, updatedData);

          const updatedFarm = {
            ...farm,
            ...updatedData,
          } as Farm;

          // Update local state
          set(state => ({
            farms: state.farms.map(farm =>
              farm.id === id ? updatedFarm : farm
            ),
            isLoading: false,
          }));

          return updatedFarm;
        } catch (error) {
          console.error('Error updating farm:', error);
          set({
            error: error instanceof Error ? error.message : 'Failed to update farm',
            isLoading: false
          });
          throw error;
        }
      },

      deleteFarm: async (id) => {
        set({ isLoading: true, error: null });
        try {
          // Delete from Firestore
          await deleteDoc(doc(firestore, 'farms', id));

          // Update local state
          set(state => ({
            farms: state.farms.filter(farm => farm.id !== id),
            isLoading: false,
          }));
        } catch (error) {
          console.error('Error deleting farm:', error);
          set({
            error: error instanceof Error ? error.message : 'Failed to delete farm',
            isLoading: false
          });
          throw error;
        }
      },

      getFarmsByStatus: (status: FarmStatus) => {
        return get().farms.filter(farm => farm.status === status);
      },

      clearFarms: () => {
        set({ farms: [], error: null });
      },
    }),
    {
      name: 'farm-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);


export const getLookupTitleLocal = (lookupId: string): string => {
  if (!lookupId) return '';
 
  const lookups = useLookupStore.getState().lookupList || [];
  const match = lookups.find((item:any) => item.id === lookupId);
 
  return match?.title || lookupId;
};
 