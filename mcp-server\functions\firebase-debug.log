[debug] [2025-07-30T07:03:21.496Z] ----------------------------------------------------------------------
[debug] [2025-07-30T07:03:21.500Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js emulators:start --only functions
[debug] [2025-07-30T07:03:21.500Z] CLI Version:   14.8.0
[debug] [2025-07-30T07:03:21.500Z] Platform:      win32
[debug] [2025-07-30T07:03:21.501Z] Node Version:  v22.14.0
[debug] [2025-07-30T07:03:21.501Z] Time:          Wed Jul 30 2025 19:03:21 GMT+1200 (New Zealand Standard Time)
[debug] [2025-07-30T07:03:21.501Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-07-30T07:03:21.829Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-30T07:03:21.830Z] > authorizing via signed-in user (<EMAIL>)
[info] i  emulators: Starting emulators: functions {"metadata":{"emulator":{"name":"hub"},"message":"Starting emulators: functions"}}
[debug] [2025-07-30T07:03:21.843Z] [logging] Logging Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-30T07:03:21.843Z] assigned listening specs for emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}]},"metadata":{"message":"assigned listening specs for emulators"}}
[debug] [2025-07-30T07:03:21.852Z] [hub] writing locator at C:\Users\<USER>\AppData\Local\Temp\hub-kissandost-9570f.json
[debug] [2025-07-30T07:03:21.875Z] [functions] Functions Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-30T07:03:21.876Z] [eventarc] Eventarc Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-30T07:03:21.876Z] [tasks] Cloud Tasks Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-30T07:03:21.876Z] late-assigned ports for functions and eventarc emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}],"functions":[{"address":"127.0.0.1","family":"IPv4","port":5001}],"eventarc":[{"address":"127.0.0.1","family":"IPv4","port":9299}],"tasks":[{"address":"127.0.0.1","family":"IPv4","port":9499}]},"metadata":{"message":"late-assigned ports for functions and eventarc emulators"}}
[warn] !  functions: The following emulators are not running, calls to these services from the Functions emulator will affect production: apphosting, auth, firestore, database, hosting, pubsub, storage, dataconnect {"metadata":{"emulator":{"name":"functions"},"message":"The following emulators are not running, calls to these services from the Functions emulator will affect production: \u001b[1mapphosting, auth, firestore, database, hosting, pubsub, storage, dataconnect\u001b[22m"}}
[debug] [2025-07-30T07:03:21.892Z] defaultcredentials: writing to file C:\Users\<USER>\AppData\Roaming\firebase\malikeahtesham111_gmail_com_application_default_credentials.json
[debug] [2025-07-30T07:03:21.895Z] Setting GAC to C:\Users\<USER>\AppData\Roaming\firebase\malikeahtesham111_gmail_com_application_default_credentials.json {"metadata":{"emulator":{"name":"functions"},"message":"Setting GAC to C:\\Users\\<USER>\\AppData\\Roaming\\firebase\\malikeahtesham111_gmail_com_application_default_credentials.json"}}
[debug] [2025-07-30T07:03:21.896Z] Checked if tokens are valid: false, expires at: 1753857441328
[debug] [2025-07-30T07:03:21.897Z] Checked if tokens are valid: false, expires at: 1753857441328
[debug] [2025-07-30T07:03:21.897Z] > refreshing access token with scopes: []
[debug] [2025-07-30T07:03:21.900Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-07-30T07:03:21.900Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-30T07:03:22.277Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-07-30T07:03:22.277Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-30T07:03:22.284Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/kissandost-9570f/adminSdkConfig [none]
[debug] [2025-07-30T07:03:23.246Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/kissandost-9570f/adminSdkConfig 200
[debug] [2025-07-30T07:03:23.246Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/kissandost-9570f/adminSdkConfig {"projectId":"kissandost-9570f","databaseURL":"https://kissandost-9570f-default-rtdb.firebaseio.com","storageBucket":"kissandost-9570f.firebasestorage.app"}
[info] i  functions: Watching "E:\python_projects_factory\animal\mcp-server\functions" for Cloud Functions... {"metadata":{"emulator":{"name":"functions"},"message":"Watching \"E:\\python_projects_factory\\animal\\mcp-server\\functions\" for Cloud Functions..."}}
[debug] [2025-07-30T07:03:23.282Z] Validating nodejs source
[debug] [2025-07-30T07:03:27.905Z] > [functions] package.json contents: {
  "name": "functions",
  "description": "Cloud Functions for Firebase",
  "scripts": {
    "serve": "firebase emulators:start --only functions",
    "shell": "firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "22"
  },
  "main": "index.js",
  "dependencies": {
    "axios": "^1.10.0",
    "cors": "^2.8.5",
    "dotenv": "^17.2.1",
    "express": "^4.18.2",
    "firebase-admin": "^12.6.0",
    "firebase-functions": "^6.0.1",
    "multer": "^1.4.5-lts.1",
    "openai": "^4.20.1",
    "uuid": "^9.0.1"
  },
  "devDependencies": {
    "firebase-functions-test": "^3.1.0"
  },
  "private": true
}
[debug] [2025-07-30T07:03:27.905Z] Building nodejs source
[debug] [2025-07-30T07:03:27.906Z] Failed to find version of module node: reached end of search path E:\python_projects_factory\animal\mcp-server\functions\node_modules
[info] +  functions: Using node@22 from host. 
[info] i  functions: Loaded environment variables from .env. 
[debug] [2025-07-30T07:03:27.910Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-07-30T07:03:27.918Z] Found firebase-functions binary at 'E:\python_projects_factory\animal\mcp-server\functions\node_modules\.bin\firebase-functions'
[info] Serving at port 8371

[info] [dotenv@17.2.1] injecting env (2) from .env -- tip: 📡 observe env with Radar: https://dotenvx.com/radar

[debug] [2025-07-30T07:03:28.526Z] Got response from /__/functions.yaml {"endpoints":{"animalApp":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","region":["australia-southeast1"],"labels":{},"httpsTrigger":{},"entryPoint":"animalApp"}},"specVersion":"v1alpha1","requiredAPIs":[],"extensions":{}}
