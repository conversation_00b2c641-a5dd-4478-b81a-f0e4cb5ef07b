
// Import service modules
const { initializeOpenAI, analyzePromptWithAI, analyzeTaskDeletionRequest, analyzeEditRequest, generateAIDietPlan, generateAIHealthPlan } = require('../services/ai-service');
const {
  // setFirestore,
  saveAnimalToDatabase,
  deleteAnimalFromDatabase,
  updateAnimalInDatabase,
  saveFarmToDatabase,
  updateFarmInDatabase,
  saveExpenseToDatabase,
  deleteExpenseFromDatabase,
  updateExpenseInDatabase,
  saveMilkingToDatabase,
  saveHealthCheckToDatabase,
  savePregnancyToDatabase,
  saveTaskToDatabase,
  updateTaskInDatabase,
  updateHealthRecordInDatabase,
  updateEmployeeInDatabase,
  deleteFarmFromDatabase,
  formatFarmDescription
} = require('../services/database-service');
const {uploadImageToFirebase, uploadBase64ImageToStorage } = require('../services/firebase-storage');
const { validatePregnancyData, findMissingPregnancyInfo, generateMissingInfoMessage } = require('../utils/pregnancyUtils');
const { createAndSaveTask, formatExpenseDescription, deleteTask } = require('../services/task-service');
const { validateRequest } = require('../validation/validation');
const { analyzeRequestType } = require('../utils/request-analyzer');
var admin = require("firebase-admin");
const { OpenAI } = require('openai');
const firestore = admin.firestore();

// Initialize OpenAI
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY // Fixed the environment variable name
});

// Lookup cache to avoid repeated database calls
let lookupCache = {};
let lookupCacheExpiry = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Session-based animal selection cache to remember selected animals
let animalSelectionCache = {};
const ANIMAL_SELECTION_CACHE_DURATION = 10 * 60 * 1000; // 10 minutes

// Helper function to get/set selected animal for a user
const getSelectedAnimal = (userId) => {
  const userSession = animalSelectionCache[userId];
  if (userSession && Date.now() - userSession.timestamp < ANIMAL_SELECTION_CACHE_DURATION) {
    return userSession.selectedAnimal;
  }
  return null;
};

const setSelectedAnimal = (userId, animal) => {
  animalSelectionCache[userId] = {
    selectedAnimal: animal,
    timestamp: Date.now()
  };
  console.log('💾 SAVED SELECTED ANIMAL TO SESSION:', { userId, animalName: animal.name, animalId: animal.id });
};

const clearSelectedAnimal = (userId) => {
  delete animalSelectionCache[userId];
  console.log('🗑️ CLEARED SELECTED ANIMAL FROM SESSION:', userId);
};

// Helper function to extract animal type from prompt
const extractAnimalTypeFromPrompt = (prompt, language = 'en') => {
  if (!prompt) return null;

  const lowerPrompt = prompt.toLowerCase();

  // English animal types
  const englishTypes = {
    'cow': ['cow', 'cattle', 'bull', 'heifer'],
    'goat': ['goat', 'kid'],
    'sheep': ['sheep', 'lamb', 'ewe', 'ram'],
    'buffalo': ['buffalo', 'water buffalo'],
    'horse': ['horse', 'mare', 'stallion', 'pony'],
    'camel': ['camel', 'dromedary'],
    'donkey': ['donkey', 'ass', 'mule'],
    'pig': ['pig', 'swine', 'hog'],
    'dog': ['dog', 'puppy'],
    'cat': ['cat', 'kitten'],
    'poultry': ['chicken', 'hen', 'rooster', 'duck', 'turkey', 'goose'],
    'fish': ['fish'],
    'rabbit': ['rabbit', 'bunny']
  };

  // Urdu animal types
  const urduTypes = {
    'cow': ['گائے', 'بیل'],
    'goat': ['بکری', 'بکرا'],
    'sheep': ['بھیڑ', 'میمنہ'],
    'buffalo': ['بھینس'],
    'horse': ['گھوڑا'],
    'camel': ['اونٹ'],
    'donkey': ['گدھا'],
    'pig': ['سور'],
    'dog': ['کتا'],
    'cat': ['بلی'],
    'poultry': ['مرغی', 'مرغا', 'بطخ'],
    'fish': ['مچھلی'],
    'rabbit': ['خرگوش']
  };

  // Check English types
  for (const [type, keywords] of Object.entries(englishTypes)) {
    if (keywords.some(keyword => lowerPrompt.includes(keyword))) {
      return type;
    }
  }

  // Check Urdu types
  for (const [type, keywords] of Object.entries(urduTypes)) {
    if (keywords.some(keyword => prompt.includes(keyword))) {
      return type;
    }
  }

  return null;
};

// Helper function to extract animal name from prompt
const extractAnimalNameFromPrompt = (prompt, availableAnimals = []) => {
  if (!prompt || !availableAnimals.length) return null;

  const lowerPrompt = prompt.toLowerCase();

  // Common patterns for mentioning animals
  const patterns = [
    /(?:health check|check health|perform health check)\s+(?:against|for|on|of)?\s+([a-zA-Z0-9_\-]+)/i,
    /(?:check|examine|analyze)\s+([a-zA-Z0-9_\-]+)/i,
    /([a-zA-Z0-9_\-]+)\s+(?:health|sick|disease)/i,
    /(?:animal|cow|goat|sheep|buffalo)\s+(?:named|called)?\s*([a-zA-Z0-9_\-]+)/i
  ];

  // Try each pattern to extract potential animal name
  for (const pattern of patterns) {
    const match = prompt.match(pattern);
    if (match && match[1]) {
      const potentialName = match[1].trim();

      // Check if this matches any available animal (case insensitive)
      const matchedAnimal = availableAnimals.find(animal =>
        animal.name && animal.name.toLowerCase() === potentialName.toLowerCase()
      );

      if (matchedAnimal) {
        console.log('🎯 FOUND ANIMAL BY NAME in prompt:', matchedAnimal.name);
        return matchedAnimal;
      }
    }
  }

  // Also check for direct name mentions (any word that matches an animal name)
  const words = lowerPrompt.split(/\s+/);
  for (const word of words) {
    const matchedAnimal = availableAnimals.find(animal =>
      animal.name && animal.name.toLowerCase() === word.toLowerCase()
    );

    if (matchedAnimal) {
      console.log('🎯 FOUND ANIMAL BY DIRECT NAME MATCH:', matchedAnimal.name);
      return matchedAnimal;
    }
  }

  return null;
};

// Helper function for animal field editing
const handleAnimalFieldEditing = async (requestBody, res, prompt, language, firestore) => {
  try {
    const selectedAnimal = requestBody.context.selectedAnimal;

    // Use AI to analyze the field update request
    const editAnalysis = await analyzeEditRequest(prompt, 'animal', language);
    console.log('Animal field edit analysis:', editAnalysis);

    // Check if this is a valid field update request
    if (!editAnalysis.isEditRequest || !editAnalysis.fieldToUpdate || !editAnalysis.newValue) {
      console.log('Invalid field update request - trying to parse manually');

      // Try to manually parse common patterns
      const lowerPrompt = prompt.toLowerCase().trim();
      let fieldToUpdate = null;
      let newValue = null;

      if (lowerPrompt.includes('change name to') || lowerPrompt.includes('name to')) {
        fieldToUpdate = 'name';
        newValue = prompt.replace(/.*(?:change name to|name to)\s*/i, '').trim();
      } else if (lowerPrompt.includes('change breed to') || lowerPrompt.includes('breed to')) {
        fieldToUpdate = 'breed';
        newValue = prompt.replace(/.*(?:change breed to|breed to)\s*/i, '').trim();
      } else if (lowerPrompt.includes('change weight to') || lowerPrompt.includes('weight to')) {
        fieldToUpdate = 'weight';
        newValue = prompt.replace(/.*(?:change weight to|weight to)\s*/i, '').trim();
      } else if (lowerPrompt.includes('change color to') || lowerPrompt.includes('color to')) {
        fieldToUpdate = 'color';
        newValue = prompt.replace(/.*(?:change color to|color to)\s*/i, '').trim();
      }

      if (!fieldToUpdate || !newValue) {
        const errorMessage = language === 'ur' ?
          `❌ تبدیلی کی درخواست سمجھ نہیں آئی۔ براہ کرم اس طرح کہیں:
• "Change name to [نیا نام]"
• "Change breed to [نئی نسل]"
• "Change weight to [نیا وزن]"
• "Change color to [نیا رنگ]"` :
          `❌ Could not understand the change request. Please say:
• "Change name to [new name]"
• "Change breed to [new breed]"
• "Change weight to [new weight]"
• "Change color to [new color]"`;

        return res.json({
          message: errorMessage,
          context: requestBody.context,
          error: false
        });
      }

      // Use manually parsed values
      editAnalysis.fieldToUpdate = fieldToUpdate;
      editAnalysis.newValue = newValue;
      console.log('Manually parsed field update:', { fieldToUpdate, newValue });
    }

    // Create update data
    const updateData = {};

    switch (editAnalysis.fieldToUpdate) {
      case 'name':
        updateData.name = editAnalysis.newValue;
        break;
      case 'species':
        updateData.species = editAnalysis.newValue;
        break;
      case 'breed':
        updateData.breed = editAnalysis.newValue;
        break;
      case 'age':
        updateData.age = editAnalysis.newValue;
        break;
      case 'gender':
        updateData.gender = editAnalysis.newValue;
        break;
      case 'weight':
        const weightMatch = editAnalysis.newValue.match(/(\d+)\s*(kg|kilogram|pound|lb)?/i);
        if (weightMatch) {
          updateData.weight = parseInt(weightMatch[1]);
          if (weightMatch[2]) {
            updateData.weightUnit = weightMatch[2].toLowerCase();
          }
        } else {
          updateData.weight = parseInt(editAnalysis.newValue) || selectedAnimal.weight;
        }
        break;
      case 'color':
        updateData.color = editAnalysis.newValue;
        break;
      case 'tagId':
        updateData.tagId = editAnalysis.newValue;
        break;
      case 'healthStatus':
        updateData.healthStatus = editAnalysis.newValue;
        break;
      default:
        updateData[editAnalysis.fieldToUpdate] = editAnalysis.newValue;
    }

    // Update the animal in database
    const animalRef = firestore.collection('farms').doc(selectedAnimal.farmId).collection('animals').doc(selectedAnimal.id);
    await animalRef.update(updateData);

    // Get updated animal data
    const updatedAnimalDoc = await animalRef.get();
    const updatedAnimal = { id: updatedAnimalDoc.id, ...updatedAnimalDoc.data() };

    const successMessage = language === 'ur' ?
      `✅ جانور کی تفصیلات اپڈیٹ ہو گئیں!

جانور: ${updatedAnimal.name}
فارم: ${selectedAnimal.farmName}
تبدیل شدہ فیلڈ: ${editAnalysis.fieldToUpdate}
نئی ویلیو: ${editAnalysis.newValue}

✅ تبدیلیاں محفوظ ہو گئیں!` :
      `✅ Animal details updated successfully!

Animal: ${updatedAnimal.name}
Farm: ${selectedAnimal.farmName}
Updated Field: ${editAnalysis.fieldToUpdate}
New Value: ${editAnalysis.newValue}

✅ Changes saved successfully!`;

    // Clear the selected animal from session after successful update
    const userId = requestBody.userId;
    if (userId) {
      clearSelectedAnimal(userId);
    }

    return res.json({
      message: successMessage,
      animalData: updatedAnimal,
      updated: true,
      databaseId: updatedAnimal.id,
      context: null // Clear context after successful update
    });

  } catch (error) {
    console.error('Error in handleAnimalFieldEditing:', error);
    return res.json({
      message: language === 'ur' ?
        'جانور کی تفصیلات اپڈیٹ کرنے میں خرابی۔ براہ کرم دوبارہ کوشش کریں۔' :
        'Error updating animal details. Please try again.',
      error: true
    });
  }
};

// Function to fetch and cache lookups
const fetchLookups = async () => {
  const now = Date.now();
  if (lookupCache && Object.keys(lookupCache).length > 0 && now < lookupCacheExpiry) {
    return lookupCache;
  }

  try {
    console.log('🔍 Fetching lookups from database...');
    const lookupsSnapshot = await firestore.collection('lookups').get();
    const allLookups = lookupsSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

    // Group lookups by category
    const groupedLookups = {};
    allLookups.forEach(lookup => {
      const categoryKey = lookup.categoryName?.toLowerCase();
      if (!categoryKey) return;

      if (!groupedLookups[categoryKey]) {
        groupedLookups[categoryKey] = {};
      }
      groupedLookups[categoryKey][lookup.id] = lookup;
    });

    lookupCache = groupedLookups;
    lookupCacheExpiry = now + CACHE_DURATION;
    console.log('✅ Lookups cached successfully');
    return lookupCache;
  } catch (error) {
    console.error('❌ Error fetching lookups:', error);
    return {};
  }
};

// Function to resolve lookup ID to readable text
const resolveLookupValue = async (lookupId, category = null) => {
  if (!lookupId) return 'Not specified';

  const lookups = await fetchLookups();

  // If category is specified, look in that category first
  if (category) {
    const categoryLookups = lookups[category.toLowerCase()];
    if (categoryLookups && categoryLookups[lookupId]) {
      return categoryLookups[lookupId].title || categoryLookups[lookupId].label || lookupId;
    }
  }

  // Search across all categories
  for (const categoryName in lookups) {
    const categoryLookups = lookups[categoryName];
    if (categoryLookups[lookupId]) {
      return categoryLookups[lookupId].title || categoryLookups[lookupId].label || lookupId;
    }
  }

  return lookupId; // Return original ID if not found
};


let previousAnimalData = null;
let previousFarmData = null;

// Helper function to check edit permissions
const checkEditPermissions = (userRole, language = 'en') => {
  if (userRole === 'caretaker') {
    const errorMessage = language === 'ur' ?
      '❌ آپ کو ایڈٹ کرنے کی اجازت نہیں ہے۔ صرف مالک اور ایڈمن ایڈٹ کر سکتے ہیں۔' :
      '❌ You do not have permission to edit. Only owners and admins can edit.';

    return {
      hasPermission: false,
      errorMessage: errorMessage
    };
  }

  return {
    hasPermission: true,
    errorMessage: null
  };
};

const fetchUserInfo = async (userId) => {
  let userData = null;
  let userRole = 'careTaker'; // Default role
  if (userId) {
    const userRef = firestore.collection('users').doc(userId);
    const userDoc = await userRef.get();
    if (userDoc.exists) {
      userData = userDoc.data();
    } else {
      // Handle user not found case if necessary
      console.warn(`User with ID ${userId} not found.`);
    }
  }

  console.log("............................", userData)
  return userData
}
// OpenAI Chat endpoint with database saving
exports.openAiChat = async (req, res) => {

  if (req.body.context) {
    console.log('📨 Context action:', req.body.context.action);
    console.log('📨 Context action:', req.body);
  }

  try {
    const requestBody = req.body;
    const { prompt, imageUri, language = 'en', userId } = requestBody;
    const userInfo = await fetchUserInfo(userId)
    const { role: userRole } = userInfo

    // Debug logging for context
    console.log('🔍 REQUEST DEBUG:', {
      prompt: prompt,
      hasContext: !!requestBody.context,
      contextKeys: requestBody.context ? Object.keys(requestBody.context) : [],
      contextAction: requestBody.context?.action,
      contextEditMode: requestBody.context?.editMode,
      contextSelectedFarm: !!requestBody.context?.selectedFarm
    });

    // Safety check: If user says "Edit" explicitly, clear any old context to start fresh
    if (prompt && prompt.toLowerCase().trim() === 'edit' && requestBody.context) {
      console.log('🧹 User said "Edit" - clearing old context to start fresh');
      requestBody.context = null;
    }

    // Handle "No", "Cancel", or similar responses - clear context
    if (prompt && requestBody.context) {
      const lowerPrompt = prompt.toLowerCase().trim();
      const cancelPatterns = [
        'no', 'cancel', 'stop', 'abort', 'quit', 'exit', 'back',
        'نہیں', 'منسوخ', 'رک', 'بند', 'واپس'
      ];

      const isCancelResponse = cancelPatterns.some(pattern =>
        lowerPrompt === pattern || lowerPrompt.includes(pattern)
      );

      if (isCancelResponse) {
        console.log('🧹 User said "No/Cancel" - clearing context');

        // Clear animal selection cache for this user
        if (userId) {
          clearSelectedAnimal(userId);
        }

        const cancelMessage = language === 'ur' ?
          '✅ عمل منسوخ کر دیا گیا۔ آپ نئی درخواست کر سکتے ہیں۔' :
          '✅ Operation cancelled. You can make a new request.';

        return res.json({
          message: cancelMessage,
          context: null, // Clear context
          cancelled: true
        });
      }
    }

    // Get previous data from request body (don't redeclare as const)
    if (requestBody.previousAnimalData) {
      previousAnimalData = requestBody.previousAnimalData;
    }
    if (requestBody.previousFarmData) {
      previousFarmData = requestBody.previousFarmData;
    }

    const validationError = validateRequest(req, openai);
    if (validationError) {
      return res.status(validationError.status).json(validationError.json);
    }

    let messages = [];

    // Handle farm save confirmation FIRST (before any other processing)
    if (previousFarmData && previousFarmData.readyToSave) {
      console.log('Processing farm confirmation with data:', previousFarmData);

      // Exclude farm creation prompts from save analysis
      const isFarmCreationPrompt = prompt.toLowerCase().includes('add this farm') ||
        prompt.toLowerCase().includes('create farm') ||
        prompt.toLowerCase().includes('farm name is');

      let shouldSave = false;
      let userIntent = null;

      if (!isFarmCreationPrompt) {
        // Use AI to analyze user intent for saving
        try {
          userIntent = await analyzePromptWithAI(prompt, 'save_intent');
          console.log('🎯 User intent analysis:', userIntent);

          shouldSave = userIntent && (userIntent.action === 'save' || userIntent.action === 'confirm');
        } catch (intentError) {
          console.error('Error analyzing save intent:', intentError);
          // Fallback to keyword matching
          const saveKeywords = language === 'ur' ?
            ['محفوظ', 'save', 'ہاں', 'اوکے', 'ٹھیک', 'شامل'] :
            ['save', 'yes', 'theek hai', 'ok', 'okay', 'go', 'add it', 'confirm', 'proceed'];

          shouldSave = saveKeywords.some(keyword =>
            prompt.toLowerCase().includes(keyword.toLowerCase())
          );
        }
      }

      const nameChangePattern = language === 'ur' ?
        /نام تبدیل کرو (.+?)(?:\s|$)/i :
        /change name to (.+?)(?:\s|$)/i;

      const nameChangeMatch = prompt.match(nameChangePattern);

      // Handle address/location change commands
      const addressChangePattern = language === 'ur' ?
        /(?:پتہ|مقام|ایڈریس) تبدیل کرو (.+?)(?:\s|$)/i :
        /change (?:address|location) to (.+?)(?:\s|$)/i;

      // Handle farm type change commands
      const typeChangePattern = language === 'ur' ?
        /(?:قسم|ٹائپ) تبدیل کرو (.+?)(?:\s|$)/i :
        /change (?:type|farm type) to (.+?)(?:\s|$)/i;

      // Handle farm size change commands
      const sizeChangePattern = language === 'ur' ?
        /(?:سائز|رقبہ) تبدیل کرو (.+?)(?:\s|$)/i :
        /change (?:size|farm size) to (.+?)(?:\s|$)/i;

      const addressChangeMatch = prompt.match(addressChangePattern);
      const typeChangeMatch = prompt.match(typeChangePattern);
      const sizeChangeMatch = prompt.match(sizeChangePattern);

      if (nameChangeMatch && nameChangeMatch[1]) {
        console.log('Updating farm name to:', nameChangeMatch[1]);

        const newName = nameChangeMatch[1].trim();
        const updatedFarmData = {
          ...previousFarmData,
          name: newName,
          readyToSave: true
        };

        const nameUpdateMessage = language === 'ur' ?
          `✅ فارم کا نام اپڈیٹ ہو گیا: ${newName}

محفوظ کرنے کے لیے "محفوظ کرو" ٹائپ کریں۔` :

          `✅ Farm name updated: ${newName}

Type "save" to save to database.`;

        return res.json({
          message: nameUpdateMessage,
          farmData: updatedFarmData
        });
      } else if (addressChangeMatch && addressChangeMatch[1]) {
        console.log('Updating farm address to:', addressChangeMatch[1]);

        const newAddress = addressChangeMatch[1].trim();
        const updatedFarmData = {
          ...previousFarmData,
          dashboardLocation: newAddress,
          readyToSave: true
        };

        // Also update the location object if it exists
        if (updatedFarmData.location) {
          updatedFarmData.location.address = newAddress;
        }

        const addressUpdateMessage = language === 'ur' ?
          `✅ فارم کا پتہ اپڈیٹ ہو گیا: ${newAddress}

محفوظ کرنے کے لیے "محفوظ کرو" ٹائپ کریں۔` :

          `✅ Farm address updated: ${newAddress}

Type "save" to save to database.`;

        return res.json({
          message: addressUpdateMessage,
          farmData: updatedFarmData
        });
      } else if (typeChangeMatch && typeChangeMatch[1]) {
        console.log('Updating farm type to:', typeChangeMatch[1]);

        const newType = typeChangeMatch[1].trim();
        const updatedFarmData = {
          ...previousFarmData,
          farmType: newType,
          readyToSave: true
        };

        const typeUpdateMessage = language === 'ur' ?
          `✅ فارم کی قسم اپڈیٹ ہو گئی: ${newType}

محفوظ کرنے کے لیے "محفوظ کرو" ٹائپ کریں۔` :

          `✅ Farm type updated: ${newType}

Type "save" to save to database.`;

        return res.json({
          message: typeUpdateMessage,
          farmData: updatedFarmData
        });
      } else if (sizeChangeMatch && sizeChangeMatch[1]) {
        console.log('Updating farm size to:', sizeChangeMatch[1]);

        const newSizeInput = sizeChangeMatch[1].trim();
        // Extract size and unit from input like "150 acres" or "50 hectares"
        const sizeMatch = newSizeInput.match(/(\d+)\s*(acre|acres|hectare|hectares|marla|kanal)?/i);

        const newSize = sizeMatch ? sizeMatch[1] : newSizeInput;
        const newUnit = sizeMatch && sizeMatch[2] ? sizeMatch[2].toLowerCase() : previousFarmData.sizeUnit || 'acre';

        const updatedFarmData = {
          ...previousFarmData,
          estimatedSize: parseInt(newSize),
          sizeUnit: newUnit,
          readyToSave: true
        };

        const sizeUpdateMessage = language === 'ur' ?
          `✅ فارم کا سائز اپڈیٹ ہو گیا: ${newSize} ${newUnit}

محفوظ کرنے کے لیے "محفوظ کرو" ٹائپ کریں۔` :

          `✅ Farm size updated: ${newSize} ${newUnit}

Type "save" to save to database.`;

        return res.json({
          message: sizeUpdateMessage,
          farmData: updatedFarmData
        });
      }

      if (shouldSave) {
        try {
          // Get lookup IDs from request body with fallbacks
          const lookupIds = {
            sizeUnit: requestBody.defaultSizeUnitId || "QmzgdLcdPP0iEFVT5LyP",
            status: requestBody.defaultStatusId || "tEbttSFNlpr6gGm5y66l",
            type: requestBody.defaultFarmTypeId || "B2bye8PZBQYqscxXoVqZ"
          };

          // Create complete farm data
          const timestamp = Date.now();
          const completeFarmData = {
            name: previousFarmData.name || `New Farm ${timestamp}`,
            description: formatFarmDescription(previousFarmData),
            location: {
              address: previousFarmData.dashboardLocation || requestBody.farmLocation || "",
              latitude: 0,
              longitude: 0
            },
            size: previousFarmData.estimatedSize || 50,
            sizeUnit: lookupIds.sizeUnit,
            type: lookupIds.type,
            status: 'active',//lookupIds.status,
            photoURL: previousFarmData.imageUri || "",
            ownerId: userId,
            createdAt: new Date(timestamp).toLocaleString('en-US', {
              timeZone: 'Asia/Karachi',
              year: 'numeric',
              month: 'long',
              day: 'numeric',
              hour: 'numeric',
              minute: '2-digit',
              second: '2-digit',
              timeZoneName: 'short'
            }),
            updatedAt: new Date(timestamp).toLocaleString('en-US', {
              timeZone: 'Asia/Karachi',
              year: 'numeric',
              month: 'long',
              day: 'numeric',
              hour: 'numeric',
              minute: '2-digit',
              second: '2-digit',
              timeZoneName: 'short'
            })
          };


          // Save to database using the existing function
          const savedFarm = await saveFarmToDatabase(completeFarmData);
          const successMessage = language === 'ur' ?
            `✅ فارم کامیابی سے محفوظ ہو گیا:
            
نام: ${completeFarmData.name}
قسم: ${previousFarmData.farmType || 'livestock farm'}
سائز: ${completeFarmData.size} ${previousFarmData.sizeUnit || 'acre'}
خصوصیات: ${previousFarmData.structures ? previousFarmData.structures.join(', ') : 'house, shed, fence'}
🆔 ڈیٹابیس ID: ${savedFarm.id}

✅ کامیابی سے ڈیٹابیس میں محفوظ ہو گیا!` :

            `✅ Farm Saved Successfully:
            
Name: ${completeFarmData.name}
Type: ${previousFarmData.farmType || 'livestock farm'}
Size: ${completeFarmData.size} ${previousFarmData.sizeUnit || 'acre'}
Features: ${previousFarmData.structures ? previousFarmData.structures.join(', ') : 'house, shed, fence'}
🆔 Database ID: ${savedFarm.id}
`;

          return res.json({
            message: successMessage,
            farmData: savedFarm,
            saved: true,
            databaseId: savedFarm.id,
            imageType: 'farm'
          });
        } catch (saveError) {
          console.error('Error saving farm:', saveError);
          return res.json({
            message: language === 'ur' ?
              'ڈیٹابیس میں محفوظ کرنے میں خرابی۔ براہ کرم دوبارہ کوشش کریں۔' :
              'Error saving to database. Please try again.',
            error: true
          });
        }
      }
    }

    // Handle animal save confirmation
    if (previousAnimalData) {
      // Use AI to analyze the prompt for actions
      const extractedInfo = await analyzePromptWithAI(prompt, 'animal_action');

      // Check for name change command using AI
      if (extractedInfo && extractedInfo.action === 'change_name' && extractedInfo.newName) {
        const newName = extractedInfo.newName.trim();
        const updatedAnimalData = {
          ...previousAnimalData,
          name: newName
        };

        try {
          // Save updated animal to database
          const savedAnimal = await saveAnimalToDatabase(updatedAnimalData);
          previousAnimalData = null; // Clear after saving

          const confirmMessage = language === 'ur' ?
            `✅  جانور کامیابی سے محفوظ ہو گیا : ${newName}\n🆔  ڈیٹابیس ID : ${savedAnimal.id}` :
            `✅  Animal Saved Successfully : ${newName}\n🆔  Database ID : ${savedAnimal.id}`;

          return res.json({
            message: confirmMessage,
            animalData: savedAnimal,
            saved: true,
            databaseId: savedAnimal.id
          });
        } catch (saveError) {
          console.error('Error saving updated animal:', saveError);
          return res.json({
            message: language === 'ur' ?
              'ڈیٹابیس میں محفوظ کرنے میں خرابی۔ براہ کرم دوبارہ کوشش کریں۔' :
              'Error saving to database. Please try again.',
            error: true
          });
        }
      }
      debugger
      // Check for save command using AI
      if (extractedInfo && (extractedInfo.action === 'save' || extractedInfo.action === 'add')) {
        try {
          const savedAnimal = await saveAnimalToDatabase(previousAnimalData);
          previousAnimalData = null; // Clear after saving

          const confirmMessage = language === 'ur' ?
            `✅  جانور کامیابی سے محفوظ ہو گیا : ${savedAnimal.name}\n🆔  ڈیٹابیس ID : ${savedAnimal.id}` :
            `✅  Animal Saved Successfully : ${savedAnimal.name}\n🆔  Database ID : ${savedAnimal.id}`;

          return res.json({
            message: confirmMessage,
            animalData: savedAnimal,
            saved: true,
            databaseId: savedAnimal.id
          });
        } catch (saveError) {
          console.error('Error saving animal:', saveError);
          return res.json({
            message: language === 'ur' ?
              'ڈیٹابیس میں محفوظ کرنے میں خرابی۔ براہ کرم دوبارہ کوشش کریں۔' :
              'Error saving to database. Please try again.',
            error: true
          });
        }
      }
    }

    // Handle task employee selection
    if (requestBody.context && requestBody.context.taskData && requestBody.context.needsEmployeeSelection) {
      console.log('Processing task employee selection...');

      try {
        const taskData = requestBody.context.taskData;

        // Check if selectedEmployeeId is provided in request body (preferred method)
        let selectedEmployeeId = requestBody.selectedEmployeeId || prompt.trim();

        // If prompt looks like an employee ID (starts with emp- or is a UUID-like string)
        if (!requestBody.selectedEmployeeId && prompt && (prompt.includes('-') || prompt.length > 10)) {
          selectedEmployeeId = prompt.trim();
        }

        console.log('Selected employee ID:', selectedEmployeeId);

        // Find the selected employee
        const usersSnapshot = await firestore.collection('users')
          .where('assignedFarmIds', 'array-contains', taskData.farmId)
          .get();

        let selectedEmployee = null;
        usersSnapshot.docs.forEach(doc => {
          if (doc.id === selectedEmployeeId) {
            const userData = doc.data();
            selectedEmployee = {
              id: doc.id,
              name: userData.name,
              role: userData.role || 'caretaker'
            };
          }
        });

        if (!selectedEmployee) {
          // If no employee found, return the selection interface again with error
          const employees = [];
          usersSnapshot.docs.forEach(doc => {
            const userData = doc.data();
            employees.push({
              id: doc.id,
              name: userData.name || 'Unnamed Employee',
              imageUri: userData.photo || userData.photoURL || null,
              role: userData.role || 'caretaker'
            });
          });

          return res.json({
            message: language === 'ur' ?
              `❌ منتخب کردہ ملازم نہیں ملا۔ براہ کرم فہرست سے انتخاب کریں:

✅ کام کی تفصیل: ${taskData.title}
🏡 فارم: ${taskData.farmName}

👥 نیچے دی گئی فہرست سے ملازم کا انتخاب کریں:` :
              `❌ Selected employee not found. Please select from the list:

✅ Task: ${taskData.title}
🏡 Farm: ${taskData.farmName}

👥 Please select an employee from the list below:`,
            needsEmployeeSelection: true,
            employeeList: employees.map(emp => ({
              id: emp.id,
              label: emp.name,
              imageUri: emp.imageUri,
              role: emp.role,
              description: `${emp.role.charAt(0).toUpperCase() + emp.role.slice(1)} • ${taskData.farmName}`
            })),
            selectionType: 'employee_dropdown',
            context: {
              taskData,
              needsEmployeeSelection: true
            },
            error: false // Not a critical error, just need reselection
          });
        }

        // Create the complete task with the selected employee
        const completeTaskData = {
          ...taskData,
          assignedTo: selectedEmployee.id,
          assigneeName: selectedEmployee.name,
          assignedBy: userId,
          assignedByName: 'System' // You might want to get the actual user name
        };

        // Create and save the task
        const result = await createAndSaveTask(completeTaskData,
          { id: taskData.farmId, name: taskData.farmName },
          selectedEmployee,
          userId,
          language);

        return res.json(result);
      } catch (error) {
        console.error('Error processing employee selection:', error);
        return res.json({
          message: language === 'ur' ?
            'ملازم کا انتخاب کرنے میں خرابی۔ براہ کرم دوبارہ کوشش کریں۔' :
            'Error processing employee selection. Please try again.',
          error: true
        });
      }
    }

    // Handle task selection for deletion
    if (requestBody.context && requestBody.context.needsTaskSelection && requestBody.context.deletionInfo) {
      console.log('Processing task selection for deletion...');

      try {
        const { deletionInfo, farmId, farmName } = requestBody.context;

        // Check if selectedTaskId is provided in request body (preferred method)
        let selectedTaskId = requestBody.selectedTaskId || prompt.trim();

        console.log('Task selection details:', {
          selectedTaskId,
          farmId,
          farmName,
          deletionInfo
        });

        if (!selectedTaskId) {
          return res.json({
            message: language === 'ur' ?
              'کوئی کام منتخب نہیں کیا گیا۔ براہ کرم فہرست سے کام منتخب کریں۔' :
              'No task selected. Please select a task from the list.',
            error: true
          });
        }

        // Import the database function
        const { deleteTaskFromDatabase } = require('../services/database-service');

        // Delete the selected task
        const deletedTask = await deleteTaskFromDatabase(selectedTaskId, farmId, userId);

        const successMessage = language === 'ur' ?
          `✅ کام کامیابی سے ڈیلیٹ ہو گیا:

عنوان: ${deletedTask.title}
تفویض کردہ: ${deletedTask.assigneeName || 'غیر تفویض کردہ'}
فارم: ${farmName}
🆔 ڈیٹابیس ID: ${deletedTask.id}

✅ کامیابی سے حذف ہو گیا!` :
          `✅ Task Successfully Deleted:

Title: ${deletedTask.title}
Assigned to: ${deletedTask.assigneeName || 'Unassigned'}
Farm: ${farmName}
🆔 Database ID: ${deletedTask.id}

✅ Successfully removed!`;

        return res.json({
          message: successMessage,
          taskData: deletedTask,
          deleted: true,
          databaseId: deletedTask.id
        });

      } catch (error) {
        console.error('Error processing task selection for deletion:', error);
        return res.json({
          message: language === 'ur' ?
            'کام حذف کرنے میں خرابی۔ براہ کرم دوبارہ کوشش کریں۔' :
            'Error deleting task. Please try again.',
          error: true
        });
      }
    }

    // Handle farm selection for editing
    if (requestBody.context && requestBody.context.action === 'edit_farm' && requestBody.context.needsFarmSelection) {
      console.log('Processing farm selection for editing...');

      try {
        // Check if selectedFarmId is provided in request body (preferred method)
        let selectedFarmId = requestBody.selectedFarmId || prompt.trim();

        // If prompt looks like a farm ID (starts with farm- or is a UUID-like string)
        if (!requestBody.selectedFarmId && prompt && (prompt.includes('-') || prompt.length > 10)) {
          selectedFarmId = prompt.trim();
        }

        console.log('Selected farm ID for editing:', selectedFarmId);
        console.log('Available farms in context:', requestBody.context?.availableFarms?.map(f => ({ id: f.id, name: f.name })));

        // Get available farms from context (preferred) or request body
        const availableFarms = requestBody.context?.availableFarms || requestBody.farms || [];

        // Find the selected farm - try multiple matching strategies
        let selectedFarm = null;
        if (selectedFarmId) {
          // First try exact ID match
          selectedFarm = availableFarms.find(farm => farm.id === selectedFarmId);

          // If not found, try case-insensitive name match
          if (!selectedFarm) {
            selectedFarm = availableFarms.find(farm =>
              farm.name && farm.name.toLowerCase() === selectedFarmId.toLowerCase()
            );
          }

          // If still not found, try partial name match
          if (!selectedFarm) {
            selectedFarm = availableFarms.find(farm =>
              farm.name && farm.name.toLowerCase().includes(selectedFarmId.toLowerCase())
            );
          }
        }

        console.log('Found selected farm:', selectedFarm ? { id: selectedFarm.id, name: selectedFarm.name } : 'null');

        if (!selectedFarm) {
          // Show the available farms again with better error message
          const farmList = availableFarms.map(farm => `• ${farm.name} (ID: ${farm.id})`).join('\n');
          const errorMessage = language === 'ur' ?
            `❌ منتخب کردہ فارم "${selectedFarmId}" نہیں ملا۔ براہ کرم نیچے دی گئی فہرست سے انتخاب کریں:

${farmList}` :
            `❌ Selected farm "${selectedFarmId}" not found. Please select from the list below:

${farmList}`;

          return res.json({
            message: errorMessage,
            farmImages: requestBody.context?.farmImages || [],
            needsFarmSelection: true,
            selectionType: 'farm_edit',
            context: requestBody.context,
            error: false
          });
        }

        // Show farm details in a structured format for editing
        // Resolve lookup values to readable text
        const farmType = await resolveLookupValue(selectedFarm.type, 'farmtype');
        const sizeUnit = await resolveLookupValue(selectedFarm.sizeUnit, 'areaunit');
        const status = await resolveLookupValue(selectedFarm.status, 'farmstatus');

        const farmDetails = {
          id: selectedFarm.id,
          name: selectedFarm.name,
          type: farmType,
          size: selectedFarm.size || 'Not specified',
          sizeUnit: sizeUnit,
          location: typeof selectedFarm.location === 'string' ? selectedFarm.location : selectedFarm.location?.address || 'Not specified',
          status: status,
          description: selectedFarm.description || 'Not provided',
          photoURL: selectedFarm.photoURL || null
        };

        // Create farm details display with image
        const farmImageDisplay = farmDetails.photoURL ?
          `![Farm Image](${farmDetails.photoURL})` :
          '📷 No image available';

        const editOptionsMessage = language === 'ur' ?
          `✏️ فارم ایڈٹ کریں: ${selectedFarm.name}

${farmImageDisplay}

📝 **موجودہ تفصیلات:**
🏷️ **نام:** ${farmDetails.name}
🏗️ **قسم:** ${farmDetails.type}
📏 **سائز:** ${farmDetails.size} ${farmDetails.sizeUnit}
📍 **مقام:** ${farmDetails.location}
📊 **حالت:** ${farmDetails.status}
📝 **تفصیل:** ${farmDetails.description}

آپ کیا تبدیل کرنا چاہتے ہیں؟ مثال:
• "نام تبدیل کرو Green Valley"
• "پتہ تبدیل کرو Main Street, Lahore"
• "قسم تبدیل کرو dairy farm"
• "سائز تبدیل کرو 200 acres"

یا تمام فیلڈز ایڈٹ کرنے کے لیے کہیں: "Give me all detail i want to edit all fields"` :
          `✏️ Edit Farm: ${selectedFarm.name}

${farmImageDisplay}

📝 **Current Details:**
🏷️ **Name:** ${farmDetails.name}
🏗️ **Type:** ${farmDetails.type}
📏 **Size:** ${farmDetails.size} ${farmDetails.sizeUnit}
📍 **Location:** ${farmDetails.location}
📊 **Status:** ${farmDetails.status}
📝 **Description:** ${farmDetails.description}

What would you like to change? Examples:
• "Change name to Green Valley"
• "Change address to Main Street, Lahore"
• "Change type to dairy farm"
• "Change size to 200 acres"

Or say: "Give me all detail i want to edit all fields" to edit all fields`;

        return res.json({
          message: editOptionsMessage,
          farmData: farmDetails,
          farmDetails: farmDetails, // Send structured farm details
          context: {
            action: 'edit_farm_fields',
            selectedFarm: selectedFarm,
            editMode: true
          },
          editMode: true,
          selectedFarm: selectedFarm,
          showFarmDetails: true // Flag to show farm details interface
        });

      } catch (error) {
        console.error('Error processing farm selection for editing:', error);
        return res.json({
          message: language === 'ur' ?
            'فارم کا انتخاب کرنے میں خرابی۔ براہ کرم دوبارہ کوشش کریں۔' :
            'Error processing farm selection. Please try again.',
          error: true
        });
      }
    }

    // Handle farm field editing after farm selection
    if (requestBody.context && requestBody.context.action === 'edit_farm_fields' && requestBody.context.selectedFarm) {
      console.log('🏡 FARM FIELD EDITING HANDLER TRIGGERED - Processing farm field editing...');
      console.log('🔍 Context details:', {
        action: requestBody.context.action,
        hasSelectedFarm: !!requestBody.context.selectedFarm,
        prompt: prompt
      });

      try {
        // Check if user wants to save pending changes
        if (prompt.toLowerCase().trim() === 'done') {
          console.log('User said "done" - saving pending changes to database');

          const pendingChanges = requestBody.context.pendingChanges;
          if (!pendingChanges || Object.keys(pendingChanges).length === 0) {
            const noChangesMessage = language === 'ur' ?
              '❌ کوئی تبدیلیاں محفوظ کرنے کے لیے نہیں ملیں۔' :
              '❌ No changes found to save.';

            return res.json({
              message: noChangesMessage,
              context: requestBody.context,
              error: false
            });
          }

          // Save pending changes to database
          const selectedFarm = requestBody.context.selectedFarm;
          const farmRef = firestore.collection('farms').doc(selectedFarm.id);

          console.log('Saving pending changes to database:', pendingChanges);
          await farmRef.update(pendingChanges);

          // Get updated farm data
          const updatedFarmDoc = await farmRef.get();
          const updatedFarm = { id: updatedFarmDoc.id, ...updatedFarmDoc.data() };

          const successMessage = language === 'ur' ?
            `✅ فارم کامیابی سے ڈیٹابیس میں محفوظ ہو گیا!

📍 نام: ${updatedFarm.name}
🏗️ قسم: ${updatedFarm.type || 'نہیں بتایا گیا'}
📏 سائز: ${updatedFarm.size || 'نہیں بتایا گیا'} ${updatedFarm.sizeUnit || ''}
📍 مقام: ${updatedFarm.location?.address || 'نہیں بتایا گیا'}
📊 حالت: ${updatedFarm.status || 'فعال'}
📝 تفصیل: ${updatedFarm.description || 'کوئی تفصیل نہیں'}

تمام تبدیلیاں کامیابی سے محفوظ ہو گئیں!` :
            `✅ Farm successfully saved to database!

📍 Name: ${updatedFarm.name}
🏗️ Type: ${updatedFarm.type || 'Not specified'}
📏 Size: ${updatedFarm.size || 'Not specified'} ${updatedFarm.sizeUnit || ''}
📍 Location: ${updatedFarm.location?.address || 'Not specified'}
📊 Status: ${updatedFarm.status || 'active'}
📝 Description: ${updatedFarm.description || 'No description'}

All changes have been saved successfully!`;

          console.log('✅ Farm saved successfully - clearing context');
          return res.json({
            message: successMessage,
            farmData: updatedFarm,
            updated: true,
            databaseId: updatedFarm.id,
            context: null, // Clear context after saving
            clearContext: true, // Explicit flag for frontend
            editComplete: true // Another flag to indicate editing is done
          });
        }

        // Use AI to analyze the edit request
        const editAnalysis = await analyzeEditRequest(prompt, 'farm', language);
        console.log('Farm edit analysis:', editAnalysis);

        if (!editAnalysis.isEditRequest) {
          const errorMessage = language === 'ur' ?
            '❌ فارم ایڈٹ کی درخواست سمجھ نہیں آئی۔ براہ کرم "Change [field] to [value]" کا استعمال کریں۔' :
            '❌ Could not understand farm edit request. Please use "Change [field] to [value]".';

          return res.json({
            message: errorMessage,
            context: requestBody.context, // Preserve context
            error: false
          });
        }

        const selectedFarm = requestBody.context.selectedFarm;

        // Update the farm with new field values
        const farmRef = firestore.collection('farms').doc(selectedFarm.id);

        // Get current farm data first
        const currentFarmDoc = await farmRef.get();
        const currentFarm = { id: currentFarmDoc.id, ...currentFarmDoc.data() };

        const updateData = {};

        // Map the field changes
        if (editAnalysis.fieldToUpdate && editAnalysis.newValue) {
          const field = editAnalysis.fieldToUpdate.toLowerCase();
          const value = editAnalysis.newValue;

          switch (field) {
            case 'name':
              updateData.name = value;
              break;
            case 'location':
            case 'address':
              // Handle nested location object properly
              updateData.location = {
                ...currentFarm.location,
                address: value
              };
              break;
            case 'description':
              updateData.description = value;
              break;
            case 'size':
              updateData.size = parseFloat(value) || 0;
              break;
            case 'type':
              updateData.type = value;
              break;
          }
        }

        // Also check for fieldChanges format (backup)
        if (editAnalysis.fieldChanges) {
          for (const [field, value] of Object.entries(editAnalysis.fieldChanges)) {
            switch (field.toLowerCase()) {
              case 'name':
                updateData.name = value;
                break;
              case 'location':
                updateData['location.address'] = value;
                break;
              case 'description':
                updateData.description = value;
                break;
              case 'size':
                updateData.size = parseFloat(value) || 0;
                break;
              case 'type':
                updateData.type = value;
                break;
            }
          }
        }

        // Check if we have any fields to update
        if (Object.keys(updateData).length === 0) {
          const errorMessage = language === 'ur' ?
            '❌ اپڈیٹ کرنے کے لیے کوئی فیلڈ نہیں ملا۔ براہ کرم "Change [field] to [value]" کا استعمال کریں۔' :
            '❌ No fields found to update. Please use "Change [field] to [value]".';

          return res.json({
            message: errorMessage,
            context: requestBody.context, // Preserve context
            error: false
          });
        }

        // Don't save to database yet - collect changes for batch update
        console.log('Collecting farm update data:', updateData);

        // Store pending changes in context for batch update
        const pendingChanges = requestBody.context?.pendingChanges || {};
        const updatedPendingChanges = { ...pendingChanges, ...updateData };

        // Show preview with pending changes and resolve lookup values
        const previewData = { ...currentFarm, ...updatedPendingChanges };

        // Resolve lookup values for display
        const farmType = await resolveLookupValue(previewData.type, 'farmtype');
        const sizeUnit = await resolveLookupValue(previewData.sizeUnit, 'areaunit');
        const status = await resolveLookupValue(previewData.status, 'farmstatus');

        const successMessage = language === 'ur' ?
          `📝 تبدیلی محفوظ کی گئی (ابھی ڈیٹابیس میں محفوظ نہیں ہوئی)

موجودہ تفصیلات (پیش نظارہ):
📍 نام: ${previewData.name}
🏗️ قسم: ${farmType || 'نہیں بتایا گیا'}
📏 سائز: ${previewData.size || 'نہیں بتایا گیا'} ${sizeUnit || ''}
📍 مقام: ${typeof previewData.location === 'string' ? previewData.location : previewData.location?.address || 'نہیں بتایا گیا'}
📊 حالت: ${status || 'فعال'}
📝 تفصیل: ${previewData.description || 'کوئی تفصیل نہیں'}

مزید تبدیلیاں کرنے کے لیے کوئی اور فیلڈ بتائیں یا تمام تبدیلیاں محفوظ کرنے کے لیے "done" کہیں۔` :
          `📝 Change saved (not yet saved to database)

Current Details (Preview):
📍 Name: ${previewData.name}
🏗️ Type: ${farmType || 'Not specified'}
📏 Size: ${previewData.size || 'Not specified'} ${sizeUnit || ''}
📍 Location: ${typeof previewData.location === 'string' ? previewData.location : previewData.location?.address || 'Not specified'}
📊 Status: ${status || 'active'}
📝 Description: ${previewData.description || 'No description'}

To make more changes, specify another field or say "done" to save all changes.`;

        return res.json({
          message: successMessage,
          farmData: previewData,
          updated: false, // Not yet saved to database
          databaseId: currentFarm.id,
          context: {
            action: 'edit_farm_fields',
            selectedFarm: currentFarm,
            pendingChanges: updatedPendingChanges, // Store pending changes
            editMode: true
          }
        });

      } catch (error) {
        console.error('Error processing farm field editing:', error);
        return res.json({
          message: language === 'ur' ?
            'فارم اپڈیٹ کرنے میں خرابی۔ براہ کرم دوبارہ کوشش کریں۔' :
            'Error updating farm. Please try again.',
          context: requestBody.context, // Preserve context
          error: false
        });
      }
    }

    // Improved module selection handling
    if (requestBody.context && requestBody.context.action === 'edit_general') {
      console.log('Processing module selection for editing...');
      
      try {
        // Normalize the input for better matching
        let selectedModuleId = (requestBody.selectedModuleId || prompt.trim().toLowerCase())
          .replace(/edit\s+/gi, '')
          .replace(/\s+details?/gi, '')
          .replace(/\s+/g, '_');

        console.log('🔍 MODULE SELECTION DEBUG:', {
          originalPrompt: prompt,
          selectedModuleId: selectedModuleId,
          hasSelectedModuleId: !!requestBody.selectedModuleId
        });

        // Enhanced module mapping with multiple variations
        const moduleMap = {
          'farm': 'edit_farm',
          'farms': 'edit_farm',
          'animal': 'edit_animal',
          'animals': 'edit_animal',
          'expense': 'edit_expense',
          'expenses': 'edit_expense',
          'health_record': 'edit_health_record',
          'health': 'edit_health_record',
          'health_records': 'edit_health_record',
          'healthrecord': 'edit_health_record',
          'task': 'edit_task',
          'tasks': 'edit_task',
          'employee': 'edit_employee',
          'employees': 'edit_employee',
          'staff': 'edit_employee'
        };

        // Try exact match first, then partial matches
        let newRequestType = moduleMap[selectedModuleId];
        
        if (!newRequestType) {
          // Try partial matching
          for (const [key, value] of Object.entries(moduleMap)) {
            if (selectedModuleId.includes(key) || key.includes(selectedModuleId)) {
              newRequestType = value;
              break;
            }
          }
        }

        if (!newRequestType) {
          const errorMessage = language === 'ur' ?
            `❌ غلط انتخاب: "${prompt}". براہ کرم صحیح ماڈیول منتخب کریں: Farm, Animal, Expense, Health Record, Task, Employee` :
            `❌ Invalid selection: "${prompt}". Please select a valid module: Farm, Animal, Expense, Health Record, Task, Employee`;

          return res.json({
            message: errorMessage,
            context: requestBody.context,
            error: false
          });
        }

        console.log('Selected edit type:', newRequestType);

        // Handle task editing (implement instead of showing "coming soon")
        if (newRequestType === 'edit_task') {
          // Get user's tasks across all farms
          const userRef = firestore.collection('users').doc(userId);
          const userDoc = await userRef.get();
          
          if (!userDoc.exists()) {
            return res.json({
              message: language === 'ur' ? 'صارف نہیں ملا۔' : 'User not found.',
              error: true
            });
          }

          const userData = userDoc.data();
          const userFarms = userData.farms || [];
          let allTasks = [];

          // Collect tasks from all user's farms
          for (const farmId of userFarms) {
            const tasksSnapshot = await firestore
              .collection('farms')
              .doc(farmId)
              .collection('tasks')
              .get();
            
            tasksSnapshot.forEach(doc => {
              allTasks.push({
                id: doc.id,
                ...doc.data(),
                farmId: farmId
              });
            });
          }

          if (allTasks.length === 0) {
            const noTasksMessage = language === 'ur' ?
              '❌ کوئی کام نہیں ملا۔ پہلے کام شامل کریں۔' :
              '❌ No tasks found. Please add tasks first.';
            
            return res.json({
              message: noTasksMessage,
              error: false
            });
          }

          // Create task selection interface
          const taskListText = allTasks.map((task, index) => 
            `${index + 1}. **${task.title}** - ${task.description} (${task.priority} priority)`
          ).join('\n');

          const selectionMessage = language === 'ur' ?
            `📋 کام ایڈٹ کریں

کون سا کام ایڈٹ کرنا چاہتے ہیں؟

${taskListText}` :
            `📋 Edit Task

Which task would you like to edit?

${taskListText}`;

          return res.json({
            message: selectionMessage,
            taskSelection: {
              tasks: allTasks.map(task => ({
                id: task.id,
                label: task.title,
                description: task.description,
                priority: task.priority,
                status: task.status,
                dueDate: task.dueDate,
                assigneeName: task.assigneeName
              })),
              context: {
                action: 'edit_task',
                needsTaskSelection: true
              }
            },
            context: {
              action: 'edit_task',
              needsTaskSelection: true
            }
          });
        }

        // Continue with other module types...
        // (Keep existing logic for farm, animal, expense)
        
      } catch (error) {
        console.error('Error processing module selection:', error);
        return res.json({
          message: language === 'ur' ?
            'ماڈیول کا انتخاب کرنے میں خرابی۔ براہ کرم دوبارہ کوشش کریں۔' :
            'Error processing module selection. Please try again.',
          error: false
        });
      }
    }

    // Handle animal selection for editing
    if (requestBody.context && requestBody.context.action === 'edit_animal' && requestBody.context.needsAnimalSelection) {
      console.log('🐄 ANIMAL SELECTION HANDLER TRIGGERED - Processing animal selection for editing...');
      console.log('🔍 Context details:', {
        action: requestBody.context.action,
        needsAnimalSelection: requestBody.context.needsAnimalSelection,
        prompt: prompt,
        hasAvailableAnimals: !!requestBody.context.availableAnimals
      });

      try {
        // SMART DETECTION: Check if this is a field edit command instead of animal selection
        const lowerPrompt = prompt.trim().toLowerCase();
        const isFieldEditCommand = lowerPrompt.includes('change ') ||
                                  lowerPrompt.includes('update ') ||
                                  lowerPrompt.includes('set ') ||
                                  lowerPrompt.includes('modify ') ||
                                  lowerPrompt.match(/\b(name|breed|weight|color|species|age|gender|tag|health)\s+to\b/i);

        if (isFieldEditCommand) {
          console.log('🔧 DETECTED FIELD EDIT COMMAND in animal selection context - redirecting to field editing');

          // First, check if we have a previously selected animal in session
          const userId = requestBody.userId;
          const sessionAnimal = userId ? getSelectedAnimal(userId) : null;

          if (sessionAnimal) {
            console.log('🎯 USING SESSION-STORED ANIMAL for field editing:', sessionAnimal.name);

            // Use the session-stored animal for field editing
            const fieldEditRequest = {
              ...requestBody,
              context: {
                action: 'edit_animal_fields',
                selectedAnimal: sessionAnimal,
                editMode: true
              }
            };

            // Call the animal field editing logic directly
            return await handleAnimalFieldEditing(fieldEditRequest, res, prompt, language, firestore);
          }

          // Fallback: Check available animals in context
          const availableAnimals = requestBody.context?.availableAnimals || [];

          console.log('🔍 No session animal found. Available animals for field editing:', {
            count: availableAnimals.length,
            animals: availableAnimals.map(a => ({ id: a.id, name: a.name }))
          });

          if (availableAnimals.length === 1) {
            // If there's only one animal, use it and save to session
            const selectedAnimal = availableAnimals[0];
            console.log('🎯 Using single available animal for field editing:', selectedAnimal.name);

            // Save to session for future use
            if (userId) {
              setSelectedAnimal(userId, selectedAnimal);
            }

            // Redirect to animal field editing handler by creating the proper context
            const fieldEditRequest = {
              ...requestBody,
              context: {
                action: 'edit_animal_fields',
                selectedAnimal: selectedAnimal,
                editMode: true
              }
            };

            // Call the animal field editing logic directly
            return await handleAnimalFieldEditing(fieldEditRequest, res, prompt, language, firestore);
          } else if (availableAnimals.length > 1) {
            // Multiple animals - show them with field edit context
            const animalList = availableAnimals.map(animal => `• ${animal.name} (ID: ${animal.id})`).join('\n');
            const errorMessage = language === 'ur' ?
              `❌ متعدد جانور دستیاب ہیں۔ پہلے جانور کا انتخاب کریں:

${animalList}

پھر دوبارہ کہیں: "${prompt}"` :
              `❌ Multiple animals available. Please select an animal first:

${animalList}

Then say again: "${prompt}"`;

            return res.json({
              message: errorMessage,
              animalImages: requestBody.context?.animalImages || [],
              needsAnimalSelection: true,
              selectionType: 'animal_edit',
              context: {
                ...requestBody.context,
                pendingFieldEdit: prompt // Remember the field edit command
              },
              error: false
            });
          } else {
            // No animals available
            const errorMessage = language === 'ur' ?
              '❌ کوئی جانور دستیاب نہیں۔ پہلے جانور شامل کریں۔' :
              '❌ No animals available. Please add animals first.';

            return res.json({
              message: errorMessage,
              error: true
            });
          }
        }

        // Normal animal selection logic
        let selectedAnimalId = requestBody.selectedAnimalId || prompt.trim();

        // If prompt looks like an animal ID (starts with animal- or is a UUID-like string)
        if (!requestBody.selectedAnimalId && prompt && (prompt.includes('-') || prompt.length > 10)) {
          selectedAnimalId = prompt.trim();
        }

        console.log('Selected animal ID for editing:', selectedAnimalId);

        // Get available animals from context (preferred) or request body
        const availableAnimals = requestBody.context?.availableAnimals || requestBody.animals || [];

        // Find the selected animal - try multiple matching strategies
        let selectedAnimal = null;
        if (selectedAnimalId) {
          // First try exact ID match
          selectedAnimal = availableAnimals.find(animal => animal.id === selectedAnimalId);

          // If not found, try case-insensitive name match
          if (!selectedAnimal) {
            selectedAnimal = availableAnimals.find(animal =>
              animal.name && animal.name.toLowerCase() === selectedAnimalId.toLowerCase()
            );
          }

          // If still not found, try partial name match
          if (!selectedAnimal) {
            selectedAnimal = availableAnimals.find(animal =>
              animal.name && animal.name.toLowerCase().includes(selectedAnimalId.toLowerCase())
            );
          }
        }

        console.log('Found selected animal:', selectedAnimal ? { id: selectedAnimal.id, name: selectedAnimal.name } : 'null');

        if (!selectedAnimal) {
          // Show the available animals again with better error message
          const animalList = availableAnimals.map(animal => `• ${animal.name} (ID: ${animal.id})`).join('\n');
          const errorMessage = language === 'ur' ?
            `❌ منتخب کردہ جانور "${selectedAnimalId}" نہیں ملا۔ براہ کرم نیچے دی گئی فہرست سے انتخاب کریں:

${animalList}` :
            `❌ Selected animal "${selectedAnimalId}" not found. Please select from the list below:

${animalList}`;

          return res.json({
            message: errorMessage,
            animalImages: requestBody.context?.animalImages || [],
            needsAnimalSelection: true,
            selectionType: 'animal_edit',
            context: requestBody.context,
            error: false
          });
        }

        // Check if there's a pending field edit command
        const pendingFieldEdit = requestBody.context?.pendingFieldEdit;
        if (pendingFieldEdit) {
          console.log('🔄 EXECUTING PENDING FIELD EDIT:', pendingFieldEdit);

          // Execute the pending field edit with the selected animal
          const fieldEditRequest = {
            ...requestBody,
            context: {
              action: 'edit_animal_fields',
              selectedAnimal: selectedAnimal,
              editMode: true
            }
          };

          // Call the animal field editing logic with the pending command
          return await handleAnimalFieldEditing(fieldEditRequest, res, pendingFieldEdit, language, firestore);
        }

        // Save the selected animal to session for future field edits
        const userId = requestBody.userId;
        if (userId) {
          setSelectedAnimal(userId, selectedAnimal);
        }

        // Show animal details and editing options
        const editOptionsMessage = language === 'ur' ?
          `✏️ جانور ایڈٹ کریں: ${selectedAnimal.name}

موجودہ تفصیلات:
🐄 نام: ${selectedAnimal.name}
🏷️ نوع: ${selectedAnimal.species || 'نہیں دیا گیا'}
🧬 نسل: ${selectedAnimal.breed || 'نہیں دی گئی'}
⚖️ وزن: ${selectedAnimal.weight || 'نہیں دیا گیا'} ${selectedAnimal.weightUnit || 'kg'}
🎨 رنگ: ${selectedAnimal.color || 'نہیں دیا گیا'}
🏷️ ٹیگ ID: ${selectedAnimal.tagId || 'نہیں دیا گیا'}
🏥 صحت: ${selectedAnimal.healthStatus || 'نہیں دی گئی'}
🏡 فارم: ${selectedAnimal.farmName}

آپ کیا تبدیل کرنا چاہتے ہیں؟ مثال:
• "نام تبدیل کرو Bella"
• "نسل تبدیل کرو Holstein"
• "وزن تبدیل کرو 450 kg"
• "رنگ تبدیل کرو Brown and White"` :
          `✏️ Edit Animal: ${selectedAnimal.name}

Current Details:
🐄 Name: ${selectedAnimal.name}
🏷️ Species: ${selectedAnimal.species || 'Not specified'}
🧬 Breed: ${selectedAnimal.breed || 'Not specified'}
⚖️ Weight: ${selectedAnimal.weight || 'Not specified'} ${selectedAnimal.weightUnit || 'kg'}
🎨 Color: ${selectedAnimal.color || 'Not specified'}
🏷️ Tag ID: ${selectedAnimal.tagId || 'Not specified'}
🏥 Health: ${selectedAnimal.healthStatus || 'Not specified'}
🏡 Farm: ${selectedAnimal.farmName}

What would you like to change? Examples:
• "Change name to Bella"
• "Change breed to Holstein"
• "Change weight to 450 kg"
• "Change color to Brown and White"`;

        return res.json({
          message: editOptionsMessage,
          animalData: selectedAnimal,
          context: {
            action: 'edit_animal_fields',
            selectedAnimal: selectedAnimal,
            editMode: true
          },
          editMode: true,
          selectedAnimal: selectedAnimal
        });

      } catch (error) {
        console.error('Error processing animal selection for editing:', error);
        return res.json({
          message: language === 'ur' ?
            'جانور کا انتخاب کرنے میں خرابی۔ براہ کرم دوبارہ کوشش کریں۔' :
            'Error processing animal selection. Please try again.',
          error: true
        });
      }
    }

    // Handle animal field editing
    if (requestBody.context && requestBody.context.action === 'edit_animal_fields' && requestBody.context.selectedAnimal) {
      console.log('🐄 ANIMAL FIELD EDITING HANDLER TRIGGERED');
      console.log('🔍 Field editing context:', {
        action: requestBody.context.action,
        selectedAnimalName: requestBody.context.selectedAnimal.name,
        selectedAnimalId: requestBody.context.selectedAnimal.id,
        prompt: prompt
      });

      try {
        const selectedAnimal = requestBody.context.selectedAnimal;

        // Use AI to analyze the field update request
        const editAnalysis = await analyzeEditRequest(prompt, 'animal', language);
        console.log('Animal field edit analysis:', editAnalysis);

        // Check if this is a valid field update request
        if (!editAnalysis.isEditRequest || !editAnalysis.fieldToUpdate || !editAnalysis.newValue) {
          console.log('Invalid field update request - trying to parse manually');

          // Try to manually parse common patterns
          const lowerPrompt = prompt.toLowerCase().trim();
          let fieldToUpdate = null;
          let newValue = null;

          if (lowerPrompt.includes('change name to') || lowerPrompt.includes('name to')) {
            fieldToUpdate = 'name';
            newValue = prompt.replace(/.*(?:change name to|name to)\s*/i, '').trim();
          } else if (lowerPrompt.includes('change breed to') || lowerPrompt.includes('breed to')) {
            fieldToUpdate = 'breed';
            newValue = prompt.replace(/.*(?:change breed to|breed to)\s*/i, '').trim();
          } else if (lowerPrompt.includes('change weight to') || lowerPrompt.includes('weight to')) {
            fieldToUpdate = 'weight';
            newValue = prompt.replace(/.*(?:change weight to|weight to)\s*/i, '').trim();
          } else if (lowerPrompt.includes('change color to') || lowerPrompt.includes('color to')) {
            fieldToUpdate = 'color';
            newValue = prompt.replace(/.*(?:change color to|color to)\s*/i, '').trim();
          }

          if (!fieldToUpdate || !newValue) {
            const errorMessage = language === 'ur' ?
              `❌ تبدیلی کی درخواست سمجھ نہیں آئی۔ براہ کرم اس طرح کہیں:
• "Change name to [نیا نام]"
• "Change breed to [نئی نسل]"
• "Change weight to [نیا وزن]"
• "Change color to [نیا رنگ]"

آپ نے کہا: "${prompt}"` :
              `❌ Could not understand field update request. Please use format:
• "Change name to [new name]"
• "Change breed to [new breed]"
• "Change weight to [new weight]"
• "Change color to [new color]"

You said: "${prompt}"`;

            return res.json({
              message: errorMessage,
              context: requestBody.context, // Keep the same context
              error: false // Not a critical error, just need clarification
            });
          }

          // Use manually parsed values
          editAnalysis.fieldToUpdate = fieldToUpdate;
          editAnalysis.newValue = newValue;
          console.log('Manually parsed field update:', { fieldToUpdate, newValue });
        }

        // Prepare update data
        const updateData = {};

        switch (editAnalysis.fieldToUpdate) {
          case 'name':
            updateData.name = editAnalysis.newValue;
            break;
          case 'species':
            updateData.species = editAnalysis.newValue;
            break;
          case 'breed':
            updateData.breed = editAnalysis.newValue;
            break;
          case 'age':
            updateData.age = editAnalysis.newValue;
            break;
          case 'gender':
            updateData.gender = editAnalysis.newValue;
            break;
          case 'weight':
            const weightMatch = editAnalysis.newValue.match(/(\d+)\s*(kg|kilogram|pound|lb)?/i);
            if (weightMatch) {
              updateData.weight = parseInt(weightMatch[1]);
              if (weightMatch[2]) {
                updateData.weightUnit = weightMatch[2].toLowerCase();
              }
            } else {
              updateData.weight = parseInt(editAnalysis.newValue) || selectedAnimal.weight;
            }
            break;
          case 'color':
            updateData.color = editAnalysis.newValue;
            break;
          case 'tagId':
            updateData.tagId = editAnalysis.newValue;
            break;
          case 'healthStatus':
            updateData.healthStatus = editAnalysis.newValue;
            break;
          default:
            updateData[editAnalysis.fieldToUpdate] = editAnalysis.newValue;
        }

        // Update the animal
        const updatedAnimal = await updateAnimalInDatabase(selectedAnimal.id, selectedAnimal.farmId, updateData, userId);

        const successMessage = language === 'ur' ?
          `✅ جانور کامیابی سے اپڈیٹ ہو گیا!

جانور: ${updatedAnimal.name}
فارم: ${selectedAnimal.farmName}
تبدیل شدہ فیلڈ: ${editAnalysis.fieldToUpdate}
نئی ویلیو: ${editAnalysis.newValue}

✅ تبدیلیاں محفوظ ہو گئیں!

مزید تبدیلیاں کرنے کے لیے دوسرا فیلڈ بتائیں یا "done" کہیں۔` :
          `✅ Animal Updated Successfully!

Animal: ${updatedAnimal.name}
Farm: ${selectedAnimal.farmName}
Updated Field: ${editAnalysis.fieldToUpdate}
New Value: ${editAnalysis.newValue}

✅ Changes saved successfully!

To make more changes, specify another field or say "done".`;

        return res.json({
          message: successMessage,
          animalData: updatedAnimal,
          updated: true,
          databaseId: updatedAnimal.id,
          context: {
            action: 'edit_animal_fields',
            selectedAnimal: { ...selectedAnimal, ...updatedAnimal }, // Update with new data
            editMode: true
          }
        });

      } catch (error) {
        console.error('Error processing animal field editing:', error);
        return res.json({
          message: language === 'ur' ?
            'جانور اپڈیٹ کرنے میں خرابی۔ براہ کرم دوبارہ کوشش کریں۔' :
            'Error updating animal. Please try again.',
          error: true
        });
      }
    }

    // Handle expense selection for editing
    if (requestBody.context && requestBody.context.action === 'edit_expense' && requestBody.context.needsExpenseSelection) {
      console.log('Processing expense selection for editing...');

      try {
        // Check if selectedExpenseId is provided in request body (preferred method)
        let selectedExpenseId = requestBody.selectedExpenseId || prompt.trim();

        // If prompt looks like an expense ID (starts with expense- or is a UUID-like string)
        if (!requestBody.selectedExpenseId && prompt && (prompt.includes('-') || prompt.length > 10)) {
          selectedExpenseId = prompt.trim();
        }

        console.log('Selected expense ID for editing:', selectedExpenseId);

        // Get available expenses from context (preferred) or request body
        const availableExpenses = requestBody.context?.availableExpenses || requestBody.expenses || [];

        // Find the selected expense
        let selectedExpense = null;
        if (selectedExpenseId) {
          selectedExpense = availableExpenses.find(expense => expense.id === selectedExpenseId);
        }

        if (!selectedExpense) {
          const errorMessage = language === 'ur' ?
            `❌ منتخب کردہ خرچہ نہیں ملا۔ براہ کرم فہرست سے انتخاب کریں۔` :
            `❌ Selected expense not found. Please select from the list.`;

          return res.json({
            message: errorMessage,
            error: true
          });
        }

        // Show expense details and editing options
        const editOptionsMessage = language === 'ur' ?
          `✏️ خرچہ ایڈٹ کریں

موجودہ تفصیلات:
💰 رقم: ${selectedExpense.currency || 'PKR'} ${selectedExpense.amount || 0}
🏷️ کیٹگری: ${selectedExpense.category || 'نہیں دی گئی'}
📝 تفصیل: ${selectedExpense.description || 'نہیں دی گئی'}
📅 تاریخ: ${new Date(selectedExpense.date || selectedExpense.createdAt).toLocaleDateString()}
💳 ادائیگی: ${selectedExpense.paymentMethod || 'نہیں دی گئی'}
🏡 فارم: ${selectedExpense.farmName}

آپ کیا تبدیل کرنا چاہتے ہیں؟ مثال:
• "رقم تبدیل کرو 5000"
• "کیٹگری تبدیل کرو Feed"
• "تفصیل تبدیل کرو Cattle feed purchase"
• "ادائیگی تبدیل کرو Cash"` :
          `✏️ Edit Expense

Current Details:
💰 Amount: ${selectedExpense.currency || 'PKR'} ${selectedExpense.amount || 0}
🏷️ Category: ${selectedExpense.category || 'Not specified'}
📝 Description: ${selectedExpense.description || 'Not provided'}
📅 Date: ${new Date(selectedExpense.date || selectedExpense.createdAt).toLocaleDateString()}
💳 Payment: ${selectedExpense.paymentMethod || 'Not specified'}
🏡 Farm: ${selectedExpense.farmName}

What would you like to change? Examples:
• "Change amount to 5000"
• "Change category to Feed"
• "Change description to Cattle feed purchase"
• "Change payment to Cash"`;

        return res.json({
          message: editOptionsMessage,
          expenseData: selectedExpense,
          context: {
            action: 'edit_expense_fields',
            selectedExpense: selectedExpense,
            editMode: true
          },
          editMode: true,
          selectedExpense: selectedExpense
        });

      } catch (error) {
        console.error('Error processing expense selection for editing:', error);
        return res.json({
          message: language === 'ur' ?
            'خرچہ کا انتخاب کرنے میں خرابی۔ براہ کرم دوبارہ کوشش کریں۔' :
            'Error processing expense selection. Please try again.',
          error: true
        });
      }
    }

    // Handle expense field editing
    if (requestBody.context && requestBody.context.action === 'edit_expense_fields' && requestBody.context.selectedExpense) {
      console.log('Processing expense field editing...');
      console.log('Selected expense from context:', requestBody.context.selectedExpense);

      try {
        const selectedExpense = requestBody.context.selectedExpense;

        // Use AI to analyze the field update request
        const editAnalysis = await analyzeEditRequest(prompt, 'expense', language);
        console.log('Expense field edit analysis:', editAnalysis);

        if (!editAnalysis.fieldToUpdate || !editAnalysis.newValue) {
          const errorMessage = language === 'ur' ?
            `❌ تبدیلی کی درخواست سمجھ نہیں آئی۔ براہ کرم اس طرح کہیں:
• "رقم تبدیل کرو [نئی رقم]"
• "کیٹگری تبدیل کرو [نئی کیٹگری]"
• "تفصیل تبدیل کرو [نئی تفصیل]"
• "ادائیگی تبدیل کرو [نیا طریقہ]"` :
            `❌ Could not understand the change request. Please say:
• "Change amount to [new amount]"
• "Change category to [new category]"
• "Change description to [new description]"
• "Change payment to [new method]"`;

          return res.json({
            message: errorMessage,
            context: requestBody.context, // Keep the same context
            error: false // Not a critical error, just need clarification
          });
        }

        // Prepare update data
        const updateData = {};

        switch (editAnalysis.fieldToUpdate) {
          case 'amount':
            const amountMatch = editAnalysis.newValue.match(/(\d+(?:\.\d+)?)/);
            if (amountMatch) {
              updateData.amount = parseFloat(amountMatch[1]);
            } else {
              updateData.amount = parseFloat(editAnalysis.newValue) || selectedExpense.amount;
            }
            break;
          case 'category':
            updateData.category = editAnalysis.newValue;
            break;
          case 'description':
            updateData.description = editAnalysis.newValue;
            break;
          case 'paymentMethod':
            updateData.paymentMethod = editAnalysis.newValue;
            break;
          case 'date':
            try {
              updateData.date = new Date(editAnalysis.newValue).getTime();
            } catch (e) {
              updateData.date = selectedExpense.date || selectedExpense.createdAt;
            }
            break;
          default:
            updateData[editAnalysis.fieldToUpdate] = editAnalysis.newValue;
        }

        // Update the expense
        const updatedExpense = await updateExpenseInDatabase(selectedExpense.id, selectedExpense.farmId, updateData, userId);

        const successMessage = language === 'ur' ?
          `✅ خرچہ کامیابی سے اپڈیٹ ہو گیا!

خرچہ: ${selectedExpense.category || 'Unknown'} - ${selectedExpense.currency || 'PKR'} ${updatedExpense.amount || selectedExpense.amount}
فارم: ${selectedExpense.farmName}
تبدیل شدہ فیلڈ: ${editAnalysis.fieldToUpdate}
نئی ویلیو: ${editAnalysis.newValue}

✅ تبدیلیاں محفوظ ہو گئیں!

مزید تبدیلیاں کرنے کے لیے دوسرا فیلڈ بتائیں یا "done" کہیں۔` :
          `✅ Expense Updated Successfully!

Expense: ${selectedExpense.category || 'Unknown'} - ${selectedExpense.currency || 'PKR'} ${updatedExpense.amount || selectedExpense.amount}
Farm: ${selectedExpense.farmName}
Updated Field: ${editAnalysis.fieldToUpdate}
New Value: ${editAnalysis.newValue}

✅ Changes saved successfully!

To make more changes, specify another field or say "done".`;

        return res.json({
          message: successMessage,
          expenseData: updatedExpense,
          updated: true,
          databaseId: updatedExpense.id,
          context: {
            action: 'edit_expense_fields',
            selectedExpense: { ...selectedExpense, ...updatedExpense }, // Update with new data
            editMode: true
          }
        });

      } catch (error) {
        console.error('Error processing expense field editing:', error);
        return res.json({
          message: language === 'ur' ?
            'خرچہ اپڈیٹ کرنے میں خرابی۔ براہ کرم دوبارہ کوشش کریں۔' :
            'Error updating expense. Please try again.',
          error: true
        });
      }
    }

    // Handle farm selection for deletion
    if (requestBody.context && requestBody.context.action === 'delete_farm' && requestBody.context.needsFarmSelection) {
      console.log('Processing farm selection for deletion...');

      try {
        // Check if selectedFarmId is provided in request body (preferred method)
        let selectedFarmId = requestBody.selectedFarmId || prompt.trim();

        // If prompt looks like a farm ID (starts with farm- or is a UUID-like string)
        if (!requestBody.selectedFarmId && prompt && (prompt.includes('-') || prompt.length > 10)) {
          selectedFarmId = prompt.trim();
        }

        console.log('Selected farm ID for deletion:', selectedFarmId);

        // Get available farms from context (preferred) or request body
        const availableFarms = requestBody.context?.availableFarms || requestBody.farms || [];

        // Find the selected farm
        let selectedFarm = null;
        if (selectedFarmId) {
          selectedFarm = availableFarms.find(farm => farm.id === selectedFarmId);
        }

        if (!selectedFarm) {
          // If no farm found, fetch all farms and return the visual selection interface
          try {
            // Get user data first to check role and permissions
            const userRef = firestore.collection('users').doc(userId);
            const userDoc = await userRef.get();

            let userData = null;
            let allUserFarms = [];

            if (userDoc.exists) {
              userData = userDoc.data();
              const assignedFarmIds = userData.assignedFarmIds || [];

              if (assignedFarmIds.length > 0) {
                // New logic: Fetch all farms from the assigned list
                const farmPromises = assignedFarmIds.map(async (farmId) => {
                  const farmDoc = await firestore.collection('farms').doc(farmId).get();
                  if (farmDoc.exists) {
                    const farmData = farmDoc.data();
                    return {
                      id: farmDoc.id,
                      name: farmData.name || 'Unnamed Farm',
                      location: farmData.location,
                      photoURL: farmData.photoURL || null,
                      ...farmData
                    };
                  }
                  return null;
                });
                const farmResults = await Promise.all(farmPromises);
                allUserFarms = farmResults.filter(farm => farm !== null);
              }
            }

            // Fallback for older owner accounts that might not have assignedFarmIds
            if (allUserFarms.length === 0 && userData?.role === 'owner') {
              console.log('No assigned farms found for owner, falling back to ownerId query.');
              const farmsSnapshot = await firestore.collection('farms')
                .where('ownerId', '==', userId)
                .get();

              farmsSnapshot.docs.forEach(doc => {
                const farmData = doc.data();
                allUserFarms.push({
                  id: doc.id,
                  name: farmData.name || 'Unnamed Farm',
                  location: farmData.location,
                  photoURL: farmData.photoURL || null,
                  ...farmData
                });
              });
            }

            const selectionMessage = language === 'ur' ?
              `❌ منتخب کردہ فارم نہیں ملا۔ براہ کرم فہرست سے انتخاب کریں:

🗑️ فارم حذف کرنا

⚠️ انتباہ: فارم حذف کرنے سے تمام متعلقہ ڈیٹا بھی حذف ہو جائے گا۔` :
              `❌ Selected farm not found. Please select from the list:

🗑️ Delete Farm

⚠️ Warning: Deleting a farm will also delete all related data.`;

            // Collect all farm images for display
            const farmImages = allUserFarms.map(farm => ({
              imageUri: farm.photoURL || null,
              name: farm.name || 'Unnamed Farm',
              location: typeof farm.location === 'string' ? farm.location :
                (farm.location?.address || 'No location specified'),
              id: farm.id,
              size: farm.size ? `${farm.size} ${farm.sizeUnit || 'acres'}` : 'Size not specified',
              type: farm.type || 'Farm'
            }));

            return res.json({
              message: selectionMessage,
              farmImages: farmImages, // Send all farm images like animal selection
              needsFarmSelection: true,
              selectionType: 'farm_deletion',
              totalFarms: allUserFarms.length,
              farmList: allUserFarms.map(farm => ({
                id: farm.id,
                label: farm.name || 'Unnamed Farm',
                description: `${typeof farm.location === 'string' ? farm.location : farm.location?.address || 'No location'} • ${farm.size || 'Unknown'} ${farm.sizeUnit || 'acres'}`,
                imageUri: farm.photoURL || null,
                type: farm.type || 'Farm',
                size: farm.size,
                sizeUnit: farm.sizeUnit
              })),
              context: {
                action: 'delete_farm',
                needsFarmSelection: true,
                availableFarms: allUserFarms
              },
              visualInterface: true, // Flag to indicate this supports visual selection
              actionType: 'destructive', // Indicates this is a destructive action
              error: false // Not a critical error, just need reselection
            });
          } catch (error) {
            console.error('Error fetching farms for reselection:', error);
            return res.json({
              message: language === 'ur' ?
                'فارمز کی فہرست لانے میں خرابی۔ براہ کرم دوبارہ کوشش کریں۔' :
                'Error fetching farms list. Please try again.',
              error: true
            });
          }
        }

        // Confirm deletion with the selected farm
        try {
          const deletedFarm = await deleteFarmFromDatabase(selectedFarm.id, userId);

          const successMessage = language === 'ur' ?
            `✅ فارم کامیابی سے حذف ہو گیا:

نام: ${deletedFarm.name}
🆔 ڈیٹابیس ID: ${deletedFarm.id}

✅ تمام متعلقہ ڈیٹا (جانور، اخراجات، دودھ کے ریکارڈ، وغیرہ) بھی حذف ہو گیا۔` :
            `✅ Farm Successfully Deleted:

Name: ${deletedFarm.name}
🆔 Database ID: ${deletedFarm.id}

✅ All related data (animals, expenses, milking records, etc.) has also been deleted.`;

          return res.json({
            message: successMessage,
            farmData: deletedFarm,
            deleted: true,
            databaseId: deletedFarm.id
          });
        } catch (deleteError) {
          console.error('Error deleting selected farm:', deleteError);

          const errorMessage = language === 'ur' ?
            `❌ فارم حذف کرنے میں خرابی: ${deleteError.message}` :
            `❌ Error deleting farm: ${deleteError.message}`;

          return res.json({
            message: errorMessage,
            error: true
          });
        }
      } catch (error) {
        console.error('Error processing farm selection for deletion:', error);
        return res.json({
          message: language === 'ur' ?
            'فارم کا انتخاب کرنے میں خرابی۔ براہ کرم دوبارہ کوشش کریں۔' :
            'Error processing farm selection. Please try again.',
          error: true
        });
      }
    }


    let requestType = analyzeRequestType(prompt, !!imageUri);

    console.log("🔍 REQUEST TYPE ANALYSIS:", {
      prompt: prompt,
      requestType: requestType,
      hasContext: !!requestBody.context,
      contextAction: requestBody.context?.action
    });

    // Handle edit requests
    if (requestType === 'edit_farm') {
      console.log('🏡 FARM EDIT HANDLER TRIGGERED - Processing farm edit request...');

      // Check user permissions - only owners and admins can edit farms
      if (userRole === 'caretaker') {
        const errorMessage = language === 'ur' ?
          '❌ آپ کو فارم ایڈٹ کرنے کی اجازت نہیں ہے۔ صرف مالک اور ایڈمن فارم ایڈٹ کر سکتے ہیں۔' :
          '❌ You do not have permission to edit farms. Only owners and admins can edit farms.';

        return res.json({
          message: errorMessage,
          error: true
        });
      }

      try {
        // Use AI to analyze the edit request
        const editAnalysis = await analyzeEditRequest(prompt, 'farm', language);
        console.log('Farm edit analysis:', editAnalysis);

        if (!editAnalysis.isEditRequest) {
          const errorMessage = language === 'ur' ?
            '❌ فارم ایڈٹ کی درخواست سمجھ نہیں آئی۔ براہ کرم "Edit [farm name]" یا "Change [field] to [value]" کا استعمال کریں۔' :
            '❌ Could not understand farm edit request. Please use "Edit [farm name]" or "Change [field] to [value]".';

          return res.json({
            message: errorMessage,
            error: true
          });
        }

        // If farm name is provided, try to find and edit that specific farm
        if (editAnalysis.farmName) {
          // Get user's farms
          const userRef = firestore.collection('users').doc(userId);
          const userDoc = await userRef.get();
          let userFarms = [];

          if (userDoc.exists) {
            const userData = userDoc.data();
            const assignedFarmIds = userData.assignedFarmIds || [];

            if (assignedFarmIds.length > 0) {
              const farmPromises = assignedFarmIds.map(async (farmId) => {
                const farmDoc = await firestore.collection('farms').doc(farmId).get();
                if (farmDoc.exists) {
                  const farmData = farmDoc.data();
                  return {
                    id: farmDoc.id,
                    name: farmData.name || 'Unnamed Farm',
                    ...farmData
                  };
                }
                return null;
              });
              const farmResults = await Promise.all(farmPromises);
              userFarms = farmResults.filter(farm => farm !== null);
            }
          }

          // Find the farm by name (case-insensitive)
          const targetFarm = userFarms.find(farm =>
            farm.name.toLowerCase().includes(editAnalysis.farmName.toLowerCase())
          );

          if (!targetFarm) {
            const errorMessage = language === 'ur' ?
              `❌ فارم "${editAnalysis.farmName}" نہیں ملا۔ براہ کرم صحیح نام استعمال کریں یا "Edit farm" کہہ کر فہرست سے انتخاب کریں۔` :
              `❌ Farm "${editAnalysis.farmName}" not found. Please use correct name or say "Edit farm" to select from list.`;

            return res.json({
              message: errorMessage,
              error: true
            });
          }

          // If specific field update is requested
          if (editAnalysis.fieldToUpdate && editAnalysis.newValue) {
            const updateData = {};

            switch (editAnalysis.fieldToUpdate) {
              case 'name':
                updateData.name = editAnalysis.newValue;
                break;
              case 'location':
              case 'address':
                updateData.location = {
                  address: editAnalysis.newValue,
                  latitude: targetFarm.location?.latitude || 0,
                  longitude: targetFarm.location?.longitude || 0
                };
                break;
              case 'type':
                updateData.type = editAnalysis.newValue;
                break;
              case 'size':
                const sizeMatch = editAnalysis.newValue.match(/(\d+)\s*(acre|acres|hectare|hectares|marla|kanal)?/i);
                if (sizeMatch) {
                  updateData.size = parseInt(sizeMatch[1]);
                  if (sizeMatch[2]) {
                    updateData.sizeUnit = sizeMatch[2].toLowerCase();
                  }
                }
                break;
              case 'description':
                updateData.description = editAnalysis.newValue;
                break;
              default:
                updateData[editAnalysis.fieldToUpdate] = editAnalysis.newValue;
            }

            // Update the farm
            const updatedFarm = await updateFarmInDatabase(targetFarm.id, updateData, userId);

            const successMessage = language === 'ur' ?
              `✅ فارم کامیابی سے اپڈیٹ ہو گیا:

نام: ${updatedFarm.name}
${editAnalysis.fieldToUpdate === 'name' ? `نیا نام: ${editAnalysis.newValue}` : ''}
${editAnalysis.fieldToUpdate === 'location' ? `نیا پتہ: ${editAnalysis.newValue}` : ''}
${editAnalysis.fieldToUpdate === 'type' ? `نئی قسم: ${editAnalysis.newValue}` : ''}
${editAnalysis.fieldToUpdate === 'size' ? `نیا سائز: ${editAnalysis.newValue}` : ''}

✅ تبدیلیاں محفوظ ہو گئیں!` :
              `✅ Farm Updated Successfully:

Name: ${updatedFarm.name}
${editAnalysis.fieldToUpdate === 'name' ? `New Name: ${editAnalysis.newValue}` : ''}
${editAnalysis.fieldToUpdate === 'location' ? `New Address: ${editAnalysis.newValue}` : ''}
${editAnalysis.fieldToUpdate === 'type' ? `New Type: ${editAnalysis.newValue}` : ''}
${editAnalysis.fieldToUpdate === 'size' ? `New Size: ${editAnalysis.newValue}` : ''}

✅ Changes saved successfully!`;

            return res.json({
              message: successMessage,
              farmData: updatedFarm,
              updated: true,
              databaseId: updatedFarm.id
            });
          }
        }

        // Check if we have a current farm context to edit directly
        // Try multiple sources for current farm ID
        const currentFarmId = requestBody.context?.currentFarmId ||
                             requestBody.context?.selectedFarm?.id ||
                             requestBody.selectedFarmId ||
                             requestBody.currentFarmId;
        console.log('Current farm context for farm edit:', currentFarmId);
        console.log('Available context sources:', {
          contextCurrentFarmId: requestBody.context?.currentFarmId,
          contextSelectedFarmId: requestBody.context?.selectedFarm?.id,
          requestSelectedFarmId: requestBody.selectedFarmId,
          requestCurrentFarmId: requestBody.currentFarmId,
          farmsCount: requestBody.farms?.length || 0
        });

        // If no current farm detected, try to use the first farm as default
        if (!currentFarmId && requestBody.farms && requestBody.farms.length > 0) {
          const defaultFarm = requestBody.farms[0];
          console.log('Using first farm as default for farm edit:', defaultFarm.name);
          // Directly edit this farm
          const farmDoc = await firestore.collection('farms').doc(defaultFarm.id).get();

          if (farmDoc.exists) {
            const farmData = farmDoc.data();
            const currentFarm = {
              id: farmDoc.id,
              name: farmData.name || 'Unnamed Farm',
              ...farmData
            };

            // Show farm details in a structured format for editing
            const farmType = await resolveLookupValue(currentFarm.type, 'farmtype');
            const sizeUnit = await resolveLookupValue(currentFarm.sizeUnit, 'areaunit');
            const status = await resolveLookupValue(currentFarm.status, 'farmstatus');

            const farmDetails = {
              id: currentFarm.id,
              name: currentFarm.name,
              type: farmType,
              size: currentFarm.size || 'Not specified',
              sizeUnit: sizeUnit,
              status: status,
              location: typeof currentFarm.location === 'string' ? currentFarm.location : currentFarm.location?.address || 'Not specified',
              description: currentFarm.description || 'No description',
              photoURL: currentFarm.photoURL || null
            };

            const editOptionsMessage = language === 'ur' ?
              `✏️ فارم ایڈٹ کریں: ${farmDetails.name}

موجودہ تفصیلات:
🏡 نام: ${farmDetails.name}
🏷️ قسم: ${farmDetails.type}
📏 سائز: ${farmDetails.size} ${farmDetails.sizeUnit}
📍 مقام: ${farmDetails.location}
📝 تفصیل: ${farmDetails.description}
📊 حالت: ${farmDetails.status}

آپ کیا تبدیل کرنا چاہتے ہیں؟ مثال:
• "نام تبدیل کرو [نیا نام]"
• "مقام تبدیل کرو [نیا پتہ]"
• "سائز تبدیل کرو [نیا سائز]"` :
              `✏️ Edit Farm: ${farmDetails.name}

Current Details:
🏡 Name: ${farmDetails.name}
🏷️ Type: ${farmDetails.type}
📏 Size: ${farmDetails.size} ${farmDetails.sizeUnit}
📍 Location: ${farmDetails.location}
📝 Description: ${farmDetails.description}
📊 Status: ${farmDetails.status}

What would you like to change? Examples:
• "Change name to [new name]"
• "Change location to [new address]"
• "Change size to [new size]"`;

            return res.json({
              message: editOptionsMessage,
              farmData: farmDetails,
              context: {
                action: 'edit_farm_fields',
                selectedFarm: currentFarm,
                editMode: true
              },
              editMode: true,
              selectedFarm: currentFarm
            });
          }
        }

        // If we have a current farm context, edit that farm directly
        if (currentFarmId) {
          console.log('Editing current farm directly:', currentFarmId);
          const farmDoc = await firestore.collection('farms').doc(currentFarmId).get();

          if (farmDoc.exists) {
            const farmData = farmDoc.data();
            const currentFarm = {
              id: farmDoc.id,
              name: farmData.name || 'Unnamed Farm',
              ...farmData
            };

            // Show farm details in a structured format for editing
            const farmType = await resolveLookupValue(currentFarm.type, 'farmtype');
            const sizeUnit = await resolveLookupValue(currentFarm.sizeUnit, 'areaunit');
            const status = await resolveLookupValue(currentFarm.status, 'farmstatus');

            const farmDetails = {
              id: currentFarm.id,
              name: currentFarm.name,
              type: farmType,
              size: currentFarm.size || 'Not specified',
              sizeUnit: sizeUnit,
              status: status,
              location: typeof currentFarm.location === 'string' ? currentFarm.location : currentFarm.location?.address || 'Not specified',
              description: currentFarm.description || 'No description',
              photoURL: currentFarm.photoURL || null
            };

            const editOptionsMessage = language === 'ur' ?
              `✏️ فارم ایڈٹ کریں: ${farmDetails.name}

موجودہ تفصیلات:
🏡 نام: ${farmDetails.name}
🏷️ قسم: ${farmDetails.type}
📏 سائز: ${farmDetails.size} ${farmDetails.sizeUnit}
📍 مقام: ${farmDetails.location}
📝 تفصیل: ${farmDetails.description}
📊 حالت: ${farmDetails.status}

آپ کیا تبدیل کرنا چاہتے ہیں؟ مثال:
• "نام تبدیل کرو [نیا نام]"
• "مقام تبدیل کرو [نیا پتہ]"
• "سائز تبدیل کرو [نیا سائز]"` :
              `✏️ Edit Farm: ${farmDetails.name}

Current Details:
🏡 Name: ${farmDetails.name}
🏷️ Type: ${farmDetails.type}
📏 Size: ${farmDetails.size} ${farmDetails.sizeUnit}
📍 Location: ${farmDetails.location}
📝 Description: ${farmDetails.description}
📊 Status: ${farmDetails.status}

What would you like to change? Examples:
• "Change name to [new name]"
• "Change location to [new address]"
• "Change size to [new size]"`;

            return res.json({
              message: editOptionsMessage,
              farmData: farmDetails,
              context: {
                action: 'edit_farm_fields',
                selectedFarm: currentFarm,
                editMode: true
              },
              editMode: true,
              selectedFarm: currentFarm
            });
          }
        }

        // If no specific farm name provided and no current farm context, show farm selection interface
        const userRef = firestore.collection('users').doc(userId);
        const userDoc = await userRef.get();
        let allUserFarms = [];

        if (userDoc.exists) {
          const userData = userDoc.data();
          const assignedFarmIds = userData.assignedFarmIds || [];

          if (assignedFarmIds.length > 0) {
            const farmPromises = assignedFarmIds.map(async (farmId) => {
              const farmDoc = await firestore.collection('farms').doc(farmId).get();
              if (farmDoc.exists) {
                const farmData = farmDoc.data();
                return {
                  id: farmDoc.id,
                  name: farmData.name || 'Unnamed Farm',
                  location: farmData.location,
                  photoURL: farmData.photoURL || null,
                  ...farmData
                };
              }
              return null;
            });
            const farmResults = await Promise.all(farmPromises);
            allUserFarms = farmResults.filter(farm => farm !== null);
          }
        }

        if (allUserFarms.length === 0) {
          const errorMessage = language === 'ur' ?
            '❌ کوئی فارم نہیں ملا۔ پہلے فارم شامل کریں۔' :
            '❌ No farms found. Please add a farm first.';

          return res.json({
            message: errorMessage,
            error: true
          });
        }

        const selectionMessage = language === 'ur' ?
          `✏️ فارم ایڈٹ کریں

کون سا فارم ایڈٹ کرنا چاہتے ہیں؟ نیچے سے انتخاب کریں:` :
          `✏️ Edit Farm

Which farm would you like to edit? Select from below:`;

        // Collect all farm images for display
        const farmImages = allUserFarms.map(farm => ({
          imageUri: farm.photoURL || null,
          name: farm.name || 'Unnamed Farm',
          location: typeof farm.location === 'string' ? farm.location :
            (farm.location?.address || 'No location specified'),
          id: farm.id,
          size: farm.size ? `${farm.size} ${farm.sizeUnit || 'acres'}` : 'Size not specified',
          type: farm.type || 'Farm'
        }));

        return res.json({
          message: selectionMessage,
          farmImages: farmImages,
          needsFarmSelection: true,
          selectionType: 'farm_edit',
          totalFarms: allUserFarms.length,
          farmList: allUserFarms.map(farm => ({
            id: farm.id,
            label: farm.name || 'Unnamed Farm',
            description: `${typeof farm.location === 'string' ? farm.location : farm.location?.address || 'No location'} • ${farm.size || 'Unknown'} ${farm.sizeUnit || 'acres'}`,
            imageUri: farm.photoURL || null,
            type: farm.type || 'Farm',
            size: farm.size,
            sizeUnit: farm.sizeUnit
          })),
          context: {
            action: 'edit_farm',
            needsFarmSelection: true,
            availableFarms: allUserFarms
          },
          visualInterface: true,
          actionType: 'edit',
          error: false
        });

      } catch (error) {
        console.error('Error processing farm edit request:', error);
        return res.json({
          message: language === 'ur' ?
            'فارم ایڈٹ کرنے میں خرابی۔ براہ کرم دوبارہ کوشش کریں۔' :
            'Error processing farm edit request. Please try again.',
          error: true
        });
      }
    }
    console.log("📨 Context received:", JSON.stringify(requestBody.context, null, 2));
    console.log("📨 Context action:", requestBody.context?.action);
    console.log("📨 Context needsAnimalSelection:", requestBody.context?.needsAnimalSelection);

    // Handle general edit request - show module selection
    if (requestType === 'edit_general') {
      console.log('✏️ GENERAL EDIT HANDLER TRIGGERED - Processing general edit request - showing module selection...');

      // Check user permissions - only owners and admins can edit
      if (userRole === 'caretaker') {
        const errorMessage = language === 'ur' ?
          '❌ آپ کو ایڈٹ کرنے کی اجازت نہیں ہے۔ صرف مالک اور ایڈمن ایڈٹ کر سکتے ہیں۔' :
          '❌ You do not have permission to edit. Only owners and admins can edit.';

        return res.json({
          message: errorMessage,
          error: true
        });
      }

      // Create module selection interface
      const editModules = [
        {
          id: 'farm',
          name: language === 'ur' ? 'فارم' : 'Farm',
          description: language === 'ur' ? 'فارم کی تفصیلات ایڈٹ کریں' : 'Edit farm details',
          icon: '🏡'
        },
        {
          id: 'animal',
          name: language === 'ur' ? 'جانور' : 'Animal',
          description: language === 'ur' ? 'جانور کی تفصیلات ایڈٹ کریں' : 'Edit animal details',
          icon: '🐄'
        },
        {
          id: 'expense',
          name: language === 'ur' ? 'خرچہ' : 'Expense',
          description: language === 'ur' ? 'خرچے کی تفصیلات ایڈٹ کریں' : 'Edit expense details',
          icon: '💰'
        },
        {
          id: 'health_record',
          name: language === 'ur' ? 'صحت کا ریکارڈ' : 'Health Record',
          description: language === 'ur' ? 'صحت کے ریکارڈ ایڈٹ کریں' : 'Edit health records',
          icon: '🏥'
        },
        {
          id: 'task',
          name: language === 'ur' ? 'کام' : 'Task',
          description: language === 'ur' ? 'کام کی تفصیلات ایڈٹ کریں' : 'Edit task details',
          icon: '📋'
        },
        {
          id: 'employee',
          name: language === 'ur' ? 'ملازم' : 'Employee',
          description: language === 'ur' ? 'ملازم کی تفصیلات ایڈٹ کریں' : 'Edit employee details',
          icon: '👤'
        }
      ];

      // Create module list text for fallback display
      const moduleListText = editModules.map(module =>
        `${module.icon} **${module.name}** - ${module.description}`
      ).join('\n');

      const selectionMessage = language === 'ur' ?
        `✏️ ایڈٹ کریں

کیا ایڈٹ کرنا چاہتے ہیں؟ نیچے سے انتخاب کریں:

${moduleListText}` :
        `✏️ Edit

What would you like to edit? Select from below:

${moduleListText}`;

      // Create module images for visual interface (similar to farmImages/animalImages)
      const moduleImages = editModules.map(module => ({
        imageUri: null, // No actual images for modules, will use icons
        name: module.name,
        description: module.description,
        id: module.id,
        icon: module.icon
      }));

      console.log('📋 Sending edit modules response:', {
        message: selectionMessage,
        editModulesCount: editModules.length,
        editModules: editModules,
        moduleImages: moduleImages,
        needsModuleSelection: true,
        selectionType: 'edit_module'
      });

      return res.json({
        message: selectionMessage,
        editModules: editModules,
        moduleImages: moduleImages, // Add this for visual interface
        needsModuleSelection: true,
        selectionType: 'edit_module',
        context: {
          action: 'select_edit_module',
          needsModuleSelection: true
        },
        visualInterface: true,
        actionType: 'edit',
        error: false
      });
    }

    // Handle module selection for editing
    if (requestBody.context && requestBody.context.action === 'select_edit_module' && requestBody.context.needsModuleSelection) {
      console.log('Processing edit module selection...');

      try {
        // Check if selectedModuleId is provided in request body (preferred method)
        let selectedModuleId = requestBody.selectedModuleId || prompt.trim().toLowerCase();

        console.log('Selected module ID for editing:', selectedModuleId);

        // Map module selection to appropriate edit type
        const moduleMap = {
          'farm': 'edit_farm',
          'animal': 'edit_animal',
          'expense': 'edit_expense',
          'health_record': 'edit_health_record',
          'task': 'edit_task',
          'employee': 'edit_employee'
        };

        if (!moduleMap[selectedModuleId]) {
          const errorMessage = language === 'ur' ?
            `❌ غلط انتخاب۔ براہ کرم صحیح ماڈیول منتخب کریں: Farm, Animal, Expense, Health Record, Task, Employee` :
            `❌ Invalid selection. Please select a valid module: Farm, Animal, Expense, Health Record, Task, Employee`;

          return res.json({
            message: errorMessage,
            context: requestBody.context, // Keep the same context
            error: false
          });
        }

        // Redirect to the appropriate edit handler by updating the request type
        const newRequestType = moduleMap[selectedModuleId];
        console.log(`Redirecting to ${newRequestType} handler...`);



        // Set the new request type and continue processing
        // Update the request type for processing
        requestType = newRequestType;

        // Create a new request with updated parameters
        // Preserve the current farm context if it exists, or try to detect it from other sources
        let preservedContext = null;

        if (requestBody.context?.selectedFarm) {
          preservedContext = {
            selectedFarm: requestBody.context.selectedFarm,
            currentFarmId: requestBody.context.selectedFarm.id
          };
        } else {
          // Try to detect current farm from other sources
          const currentFarmId = requestBody.selectedFarmId || requestBody.currentFarmId;
          if (currentFarmId && requestBody.farms) {
            const currentFarm = requestBody.farms.find(farm => farm.id === currentFarmId);
            if (currentFarm) {
              preservedContext = {
                selectedFarm: currentFarm,
                currentFarmId: currentFarm.id
              };
              console.log('Detected current farm from request:', currentFarm.name);
            }
          } else if (requestBody.farms && requestBody.farms.length > 0) {
            // If user has farms, use the first one as default
            const defaultFarm = requestBody.farms[0];
            preservedContext = {
              selectedFarm: defaultFarm,
              currentFarmId: defaultFarm.id
            };
            console.log('Using first farm as default:', defaultFarm.name);
          }
        }

        const updatedRequest = {
          ...req,
          body: {
            ...requestBody,
            prompt: `edit ${selectedModuleId}`,
            context: preservedContext // Preserve farm context for filtering
          }
        };

        // Continue processing with the new request type
        if (newRequestType === 'edit_farm') {
          // Recursively call the same function with updated parameters
          return exports.openAiChat(updatedRequest, res);
        } else if (newRequestType === 'edit_animal') {
          // Recursively call the same function with updated parameters
          return exports.openAiChat(updatedRequest, res);
        } else if (newRequestType === 'edit_expense') {
          // Recursively call the same function with updated parameters
          return exports.openAiChat(updatedRequest, res);
        } else {
          // For other modules, show coming soon message
          const comingSoonMessage = language === 'ur' ?
            `✏️ ${selectedModuleId === 'health_record' ? 'صحت کے ریکارڈ' : selectedModuleId === 'task' ? 'کام' : 'ملازم'} ایڈٹ کرنے کی سہولت جلد آ رہی ہے۔` :
            `✏️ ${selectedModuleId.replace('_', ' ')} editing feature coming soon.`;

          return res.json({
            message: comingSoonMessage,
            error: false
          });
        }

      } catch (error) {
        console.error('Error processing edit module selection:', error);
        return res.json({
          message: language === 'ur' ?
            'ماڈیول کا انتخاب کرنے میں خرابی۔ براہ کرم دوبارہ کوشش کریں۔' :
            'Error processing module selection. Please try again.',
          error: true
        });
      }
    }

    // Handle edit animal requests
    if (requestType === 'edit_animal') {
      console.log('Processing animal edit request...');

      // Check user permissions - only owners and admins can edit animals
      if (userRole === 'caretaker') {
        const errorMessage = language === 'ur' ?
          '❌ آپ کو جانور ایڈٹ کرنے کی اجازت نہیں ہے۔ صرف مالک اور ایڈمن جانور ایڈٹ کر سکتے ہیں۔' :
          '❌ You do not have permission to edit animals. Only owners and admins can edit animals.';

        return res.json({
          message: errorMessage,
          error: true
        });
      }

      try {
        // Check if this is just "Edit Animal" or "Animal" - show animal list
        const lowerPrompt = prompt.trim().toLowerCase();
        let editAnalysis = null;
        let showAnimalList = false;

        if (lowerPrompt === 'animal' || lowerPrompt === 'animals' ||
            lowerPrompt === 'edit animal' || lowerPrompt === 'edit animals') {
          console.log('User selected animal module - showing animal list for selection');
          showAnimalList = true;
          // Create a default editAnalysis for list display
          editAnalysis = { isEditRequest: false, animalName: null };
        } else {
          // Use AI to analyze the edit request for specific animal editing
          editAnalysis = await analyzeEditRequest(prompt, 'animal', language);
          console.log('Animal edit analysis:', editAnalysis);

          if (!editAnalysis.isEditRequest) {
            const errorMessage = language === 'ur' ?
              '❌ جانور ایڈٹ کی درخواست سمجھ نہیں آئی۔ براہ کرم "Edit [animal name]" یا "Change [field] to [value]" کا استعمال کریں۔' :
              '❌ Could not understand animal edit request. Please use "Edit [animal name]" or "Change [field] to [value]".';

            return res.json({
              message: errorMessage,
              error: true
            });
          }
        }

        // Get user's farms and animals
        const userRef = firestore.collection('users').doc(userId);
        const userDoc = await userRef.get();
        let userAnimals = [];
        let userFarms = [];

        // Check if we have a current farm context to filter by
        // Try multiple sources for current farm ID
        const currentFarmId = requestBody.context?.currentFarmId ||
                             requestBody.context?.selectedFarm?.id ||
                             requestBody.selectedFarmId ||
                             requestBody.currentFarmId;
        console.log('Current farm context for animal edit:', currentFarmId);
        console.log('Available context sources for animals:', {
          contextCurrentFarmId: requestBody.context?.currentFarmId,
          contextSelectedFarmId: requestBody.context?.selectedFarm?.id,
          requestSelectedFarmId: requestBody.selectedFarmId,
          requestCurrentFarmId: requestBody.currentFarmId,
          farmsCount: requestBody.farms?.length || 0
        });

        // If no current farm detected, try to use the first farm as default
        if (!currentFarmId && requestBody.farms && requestBody.farms.length > 0) {
          const defaultFarm = requestBody.farms[0];
          console.log('Using first farm as default for animal edit:', defaultFarm.name);
          // Set the current farm ID to the default farm
          currentFarmId = defaultFarm.id;
        }

        if (userDoc.exists) {
          const userData = userDoc.data();
          let assignedFarmIds = userData.assignedFarmIds || [];

          // If we have a current farm context, filter to only that farm
          if (currentFarmId) {
            assignedFarmIds = assignedFarmIds.filter(farmId => farmId === currentFarmId);
            console.log('Filtering animals to current farm only:', currentFarmId);
          }

          if (assignedFarmIds.length > 0) {
            // Get farms first
            const farmPromises = assignedFarmIds.map(async (farmId) => {
              const farmDoc = await firestore.collection('farms').doc(farmId).get();
              if (farmDoc.exists) {
                const farmData = farmDoc.data();
                return {
                  id: farmDoc.id,
                  name: farmData.name || 'Unnamed Farm',
                  ...farmData
                };
              }
              return null;
            });
            const farmResults = await Promise.all(farmPromises);
            userFarms = farmResults.filter(farm => farm !== null);

            // Get animals from the specified farms (current farm only if context exists)
            const animalPromises = assignedFarmIds.map(async (farmId) => {
              const farmRef = firestore.collection('farms').doc(farmId);
              const animalsSnapshot = await farmRef.collection('animals').get();

              const farmAnimals = [];
              animalsSnapshot.docs.forEach(doc => {
                const animalData = doc.data();
                farmAnimals.push({
                  id: doc.id,
                  farmId: farmId,
                  farmName: userFarms.find(f => f.id === farmId)?.name || 'Unknown Farm',
                  name: animalData.name || 'Unnamed Animal',
                  species: animalData.species || 'Unknown Species',
                  ...animalData
                });
              });

              return farmAnimals;
            });

            const animalResults = await Promise.all(animalPromises);
            userAnimals = animalResults.flat();
          }
        }

        // If animal name is provided, try to find and edit that specific animal
        if (editAnalysis.animalName) {
          // Find the animal by name (case-insensitive)
          const targetAnimal = userAnimals.find(animal =>
            animal.name.toLowerCase().includes(editAnalysis.animalName.toLowerCase())
          );

          if (!targetAnimal) {
            const errorMessage = language === 'ur' ?
              `❌ جانور "${editAnalysis.animalName}" نہیں ملا۔ براہ کرم صحیح نام استعمال کریں یا "Edit animal" کہہ کر فہرست سے انتخاب کریں۔` :
              `❌ Animal "${editAnalysis.animalName}" not found. Please use correct name or say "Edit animal" to select from list.`;

            return res.json({
              message: errorMessage,
              error: true
            });
          }

          // If specific field update is requested
          if (editAnalysis.fieldToUpdate && editAnalysis.newValue) {
            const updateData = {};

            switch (editAnalysis.fieldToUpdate) {
              case 'name':
                updateData.name = editAnalysis.newValue;
                break;
              case 'species':
                updateData.species = editAnalysis.newValue;
                break;
              case 'breed':
                updateData.breed = editAnalysis.newValue;
                break;
              case 'age':
                updateData.age = editAnalysis.newValue;
                break;
              case 'gender':
                updateData.gender = editAnalysis.newValue;
                break;
              case 'weight':
                const weightMatch = editAnalysis.newValue.match(/(\d+)\s*(kg|kilogram|pound|lb)?/i);
                if (weightMatch) {
                  updateData.weight = parseInt(weightMatch[1]);
                  if (weightMatch[2]) {
                    updateData.weightUnit = weightMatch[2].toLowerCase();
                  }
                } else {
                  updateData.weight = parseInt(editAnalysis.newValue) || targetAnimal.weight;
                }
                break;
              case 'color':
                updateData.color = editAnalysis.newValue;
                break;
              case 'tagId':
                updateData.tagId = editAnalysis.newValue;
                break;
              case 'healthStatus':
                updateData.healthStatus = editAnalysis.newValue;
                break;
              default:
                updateData[editAnalysis.fieldToUpdate] = editAnalysis.newValue;
            }

            // Update the animal
            const updatedAnimal = await updateAnimalInDatabase(targetAnimal.id, targetAnimal.farmId, updateData, userId);

            const successMessage = language === 'ur' ?
              `✅ جانور کامیابی سے اپڈیٹ ہو گیا:

نام: ${updatedAnimal.name}
فارم: ${targetAnimal.farmName}
${editAnalysis.fieldToUpdate === 'name' ? `نیا نام: ${editAnalysis.newValue}` : ''}
${editAnalysis.fieldToUpdate === 'species' ? `نئی نوع: ${editAnalysis.newValue}` : ''}
${editAnalysis.fieldToUpdate === 'breed' ? `نئی نسل: ${editAnalysis.newValue}` : ''}
${editAnalysis.fieldToUpdate === 'weight' ? `نیا وزن: ${editAnalysis.newValue}` : ''}

✅ تبدیلیاں محفوظ ہو گئیں!` :
              `✅ Animal Updated Successfully:

Name: ${updatedAnimal.name}
Farm: ${targetAnimal.farmName}
${editAnalysis.fieldToUpdate === 'name' ? `New Name: ${editAnalysis.newValue}` : ''}
${editAnalysis.fieldToUpdate === 'species' ? `New Species: ${editAnalysis.newValue}` : ''}
${editAnalysis.fieldToUpdate === 'breed' ? `New Breed: ${editAnalysis.newValue}` : ''}
${editAnalysis.fieldToUpdate === 'weight' ? `New Weight: ${editAnalysis.newValue}` : ''}

✅ Changes saved successfully!`;

            return res.json({
              message: successMessage,
              animalData: updatedAnimal,
              updated: true,
              databaseId: updatedAnimal.id
            });
          }
        }

        // If no specific animal name provided, show animal selection interface
        if (userAnimals.length === 0) {
          const errorMessage = language === 'ur' ?
            '❌ کوئی جانور نہیں ملا۔ پہلے جانور شامل کریں۔' :
            '❌ No animals found. Please add animals first.';

          return res.json({
            message: errorMessage,
            error: true
          });
        }

        const selectionMessage = language === 'ur' ?
          `✏️ جانور ایڈٹ کریں

کون سا جانور ایڈٹ کرنا چاہتے ہیں؟ نیچے سے انتخاب کریں:` :
          `✏️ Edit Animal

Which animal would you like to edit? Select from below:`;

        // Collect all animal images for display
        const animalImages = userAnimals.map(animal => ({
          imageUri: animal.imageUri || null,
          name: animal.name || 'Unnamed Animal',
          species: animal.species || 'Unknown Species',
          id: animal.id,
          farmId: animal.farmId,
          farmName: animal.farmName,
          breed: animal.breed || 'Unknown Breed',
          age: animal.age || 'Unknown Age'
        }));

        return res.json({
          message: selectionMessage,
          animalImages: animalImages,
          needsAnimalSelection: true,
          selectionType: 'animal_edit',
          totalAnimals: userAnimals.length,
          animalList: userAnimals.map(animal => ({
            id: animal.id,
            label: animal.name || 'Unnamed Animal',
            description: `${animal.species || 'Unknown'} • ${animal.farmName} • ${animal.breed || 'Unknown breed'}`,
            imageUri: animal.imageUri || null,
            species: animal.species,
            farmId: animal.farmId,
            farmName: animal.farmName
          })),
          context: {
            action: 'edit_animal',
            needsAnimalSelection: true,
            availableAnimals: userAnimals
          },
          visualInterface: true,
          actionType: 'edit',
          error: false
        });

      } catch (error) {
        console.error('Error processing animal edit request:', error);
        return res.json({
          message: language === 'ur' ?
            'جانور ایڈٹ کرنے میں خرابی۔ براہ کرم دوبارہ کوشش کریں۔' :
            'Error processing animal edit request. Please try again.',
          error: true
        });
      }
    }

    // Handle edit expense requests
    if (requestType === 'edit_expense') {
      console.log('Processing expense edit request...');

      // Check user permissions - only owners and admins can edit expenses
      if (userRole === 'caretaker') {
        const errorMessage = language === 'ur' ?
          '❌ آپ کو خرچہ ایڈٹ کرنے کی اجازت نہیں ہے۔ صرف مالک اور ایڈمن خرچہ ایڈٹ کر سکتے ہیں۔' :
          '❌ You do not have permission to edit expenses. Only owners and admins can edit expenses.';

        return res.json({
          message: errorMessage,
          error: true
        });
      }

      try {
        // Check if this is just "Expense" from module selection - show expense list
        const lowerPrompt = prompt.trim().toLowerCase();
        let editAnalysis = null;
        let showExpenseList = false;

        if (lowerPrompt === 'expense' || lowerPrompt === 'expenses' ||
            lowerPrompt === 'edit expense' || lowerPrompt === 'edit expenses') {
          console.log('User selected expense module - showing expense list for selection');
          showExpenseList = true;
          // Create a default editAnalysis for list display
          editAnalysis = { isEditRequest: false, expenseName: null };
        } else {
          // Use AI to analyze the edit request for specific expense editing
          editAnalysis = await analyzeEditRequest(prompt, 'expense', language);
          console.log('Expense edit analysis:', editAnalysis);

          if (!editAnalysis.isEditRequest) {
            const errorMessage = language === 'ur' ?
              '❌ خرچہ ایڈٹ کی درخواست سمجھ نہیں آئی۔ براہ کرم "Edit expense" یا "Change [field] to [value]" کا استعمال کریں۔' :
              '❌ Could not understand expense edit request. Please use "Edit expense" or "Change [field] to [value]".';

            return res.json({
              message: errorMessage,
              error: true
            });
          }
        }

        // Get user's farms and expenses
        const userRef = firestore.collection('users').doc(userId);
        const userDoc = await userRef.get();
        let userExpenses = [];
        let userFarms = [];

        // Check if we have a current farm context to filter by
        // Try multiple sources for current farm ID
        const currentFarmId = requestBody.context?.currentFarmId ||
                             requestBody.context?.selectedFarm?.id ||
                             requestBody.selectedFarmId ||
                             requestBody.currentFarmId;
        console.log('Current farm context for expense edit:', currentFarmId);
        console.log('Available context sources for expenses:', {
          contextCurrentFarmId: requestBody.context?.currentFarmId,
          contextSelectedFarmId: requestBody.context?.selectedFarm?.id,
          requestSelectedFarmId: requestBody.selectedFarmId,
          requestCurrentFarmId: requestBody.currentFarmId,
          farmsCount: requestBody.farms?.length || 0
        });
        console.log('Request body farms:', requestBody.farms);

        // If no current farm detected, try to use the first farm as default
        let finalCurrentFarmId = currentFarmId;
        if (!finalCurrentFarmId && requestBody.farms && requestBody.farms.length > 0) {
          const defaultFarm = requestBody.farms[0];
          console.log('Using first farm as default for expense edit:', defaultFarm.name);
          // Set the current farm ID to the default farm
          finalCurrentFarmId = defaultFarm.id;
        }

        // Use farms from request body if available, otherwise fall back to assignedFarmIds
        let farmsToQuery = [];
        if (requestBody.farms && requestBody.farms.length > 0) {
          // If we have a current farm context, filter to only that farm
          if (finalCurrentFarmId) {
            farmsToQuery = requestBody.farms.filter(farm => farm.id === finalCurrentFarmId);
            console.log('Filtering expenses to current farm only:', finalCurrentFarmId);
          } else {
            farmsToQuery = requestBody.farms;
            console.log('Using all farms from request body for expense edit');
          }
        } else if (userDoc.exists) {
          const userData = userDoc.data();
          let assignedFarmIds = userData.assignedFarmIds || [];

          // If we have a current farm context, filter to only that farm
          if (finalCurrentFarmId) {
            assignedFarmIds = assignedFarmIds.filter(farmId => farmId === finalCurrentFarmId);
            console.log('Filtering expenses to current farm only:', finalCurrentFarmId);
          }

          // Convert assignedFarmIds to farm objects
          farmsToQuery = assignedFarmIds.map(farmId => ({ id: farmId }));
        }

        console.log('Farms to query for expenses:', farmsToQuery);

        if (farmsToQuery.length > 0) {
          // Get farms first (if they don't already have name property)
          const farmPromises = farmsToQuery.map(async (farm) => {
            if (farm.name) {
              // Farm already has name, use it directly
              return farm;
            } else {
              // Fetch farm data from Firestore
              const farmDoc = await firestore.collection('farms').doc(farm.id).get();
              if (farmDoc.exists) {
                const farmData = farmDoc.data();
                return {
                  id: farmDoc.id,
                  name: farmData.name || 'Unnamed Farm',
                  ...farmData
                };
              }
              return null;
            }
          });
          const farmResults = await Promise.all(farmPromises);
          userFarms = farmResults.filter(farm => farm !== null);
          console.log('User farms resolved:', userFarms.map(f => ({ id: f.id, name: f.name })));

          // Get expenses from the specified farms
          const expensePromises = farmsToQuery.map(async (farm) => {
            console.log(`Fetching expenses for farm: ${farm.id} (${farm.name || 'Unknown'})`);
            const farmRef = firestore.collection('farms').doc(farm.id);

            // Try different ordering strategies since expenses might use different timestamp fields
            let expensesSnapshot;
            try {
              // First try ordering by createdAtTimestamp (newer format)
              expensesSnapshot = await farmRef.collection('expenses').orderBy('createdAtTimestamp', 'desc').get();
            } catch (error) {
              console.log('createdAtTimestamp field not found, trying createdAt');
              try {
                // Fallback to createdAt
                expensesSnapshot = await farmRef.collection('expenses').orderBy('createdAt', 'desc').get();
              } catch (error2) {
                console.log('createdAt field not found, getting all expenses without ordering');
                // Final fallback - get all expenses without ordering
                expensesSnapshot = await farmRef.collection('expenses').get();
              }
            }

            console.log(`Found ${expensesSnapshot.docs.length} expenses for farm ${farm.id}`);

            const farmExpenses = [];
            expensesSnapshot.docs.forEach(doc => {
              const expenseData = doc.data();
              console.log(`Expense found: ${doc.id}`, {
                amount: expenseData.amount,
                category: expenseData.category,
                farmId: farm.id
              });
              farmExpenses.push({
                id: doc.id,
                farmId: farm.id,
                farmName: userFarms.find(f => f.id === farm.id)?.name || farm.name || 'Unknown Farm',
                ...expenseData
              });
            });

            return farmExpenses;
          });

          const expenseResults = await Promise.all(expensePromises);
          userExpenses = expenseResults.flat();
          console.log(`Total expenses found across all farms: ${userExpenses.length}`);
        } else {
          console.log('No farms to query for expenses');
        }

        // If no expenses found, show helpful message with option to add expenses
        if (userExpenses.length === 0) {
          const farmName = farmsToQuery.length > 0 ? (farmsToQuery[0].name || 'your farm') : 'your farm';
          const errorMessage = language === 'ur' ?
            `❌ ${farmName} میں کوئی خرچہ نہیں ملا۔

پہلے خرچہ شامل کرنے کے لیے "Add Expense" کہیں۔` :
            `❌ No expenses found in ${farmName}.

To add your first expense, say "Add Expense".`;

          return res.json({
            message: errorMessage,
            error: true,
            suggestedAction: 'add_expense',
            farmContext: {
              farmId: finalCurrentFarmId,
              farmName: farmName
            }
          });
        }

        const selectionMessage = language === 'ur' ?
          `✏️ خرچہ ایڈٹ کریں

کون سا خرچہ ایڈٹ کرنا چاہتے ہیں؟ نیچے سے انتخاب کریں:` :
          `✏️ Edit Expense

Which expense would you like to edit? Select from below:`;

        return res.json({
          message: selectionMessage,
          needsExpenseSelection: true,
          selectionType: 'expense_edit',
          totalExpenses: userExpenses.length,
          expenseImages: userExpenses.map(expense => ({
            id: expense.id,
            name: `${expense.category || 'Unknown'} - ${expense.currency || 'PKR'} ${expense.amount || 0}`,
            imageUri: expense.receiptImage || null,
            amount: `${expense.currency || 'PKR'} ${expense.amount || 0}`,
            date: new Date(expense.date || expense.createdAt).toLocaleDateString(),
            paymentMethod: expense.paymentMethod || 'Unknown',
            category: expense.category || 'Unknown',
            description: expense.description || 'No description',
            farmId: expense.farmId,
            farmName: expense.farmName
          })),
          context: {
            action: 'edit_expense',
            needsExpenseSelection: true,
            availableExpenses: userExpenses
          },
          visualInterface: true,
          actionType: 'edit',
          error: false
        });

      } catch (error) {
        console.error('Error processing expense edit request:', error);
        return res.json({
          message: language === 'ur' ?
            'خرچہ ایڈٹ کرنے میں خرابی۔ براہ کرم دوبارہ کوشش کریں۔' :
            'Error processing expense edit request. Please try again.',
          error: true
        });
      }
    }

    // Handle edit health record requests
    if (requestType === 'edit_health_record') {
      console.log('Processing health record edit request...');

      const permissionCheck = checkEditPermissions(userRole, language);
      if (!permissionCheck.hasPermission) {
        return res.json({
          message: permissionCheck.errorMessage,
          error: true
        });
      }

      const errorMessage = language === 'ur' ?
        '✏️ صحت کے ریکارڈ ایڈٹ کرنے کی سہولت جلد آ رہی ہے۔ فی الوقت آپ نئے ریکارڈ شامل کر سکتے ہیں۔' :
        '✏️ Health record editing feature coming soon. For now, you can add new health records.';

      return res.json({
        message: errorMessage,
        error: false
      });
    }

    // Handle edit task requests
    if (requestType === 'edit_task') {
      console.log('Processing task edit request...');

      const permissionCheck = checkEditPermissions(userRole, language);
      if (!permissionCheck.hasPermission) {
        return res.json({
          message: permissionCheck.errorMessage,
          error: true
        });
      }

      const errorMessage = language === 'ur' ?
        '✏️ کام ایڈٹ کرنے کی سہولت جلد آ رہی ہے۔ فی الوقت آپ نئے کام شامل کر سکتے ہیں۔' :
        '✏️ Task editing feature coming soon. For now, you can add new tasks.';

      return res.json({
        message: errorMessage,
        error: false
      });
    }

    // Handle edit employee requests
    if (requestType === 'edit_employee') {
      console.log('Processing employee edit request...');

      // Only owners can edit employees
      if (userRole !== 'owner') {
        const errorMessage = language === 'ur' ?
          '❌ آپ کو ملازم ایڈٹ کرنے کی اجازت نہیں ہے۔ صرف مالک ملازم ایڈٹ کر سکتے ہیں۔' :
          '❌ You do not have permission to edit employees. Only owners can edit employees.';

        return res.json({
          message: errorMessage,
          error: true
        });
      }

      const errorMessage = language === 'ur' ?
        '✏️ ملازم ایڈٹ کرنے کی سہولت جلد آ رہی ہے۔ فی الوقت آپ نئے ملازم شامل کر سکتے ہیں۔' :
        '✏️ Employee editing feature coming soon. For now, you can add new employees.';

      return res.json({
        message: errorMessage,
        error: false
      });
    }

    // Handle text-only health check requests (validation flow)
    if (!imageUri && prompt) {
      const lowerPrompt = prompt.toLowerCase();

      // Check for health check commands in English and Urdu
      const healthCheckPatterns = [
        'perform health check',
        'health check',
        'check health',
        'صحت چیک کریں',
        'جانور کی صحت دیکھیں',
        'صحت کا معائنہ',
        'صحت کی جانچ'
      ];

      const isHealthCheckRequest = healthCheckPatterns.some(pattern =>
        lowerPrompt.includes(pattern.toLowerCase())
      );

      if (isHealthCheckRequest) {
        console.log('🏥 TEXT-ONLY HEALTH CHECK REQUEST DETECTED');

        const validationMessage = language === 'ur' ?
          '📸 براہ کرم جانور کی تصویر اپ لوڈ کریں یا کھینچیں تاکہ AI صحت کی جانچ کر سکے۔\n\n🔍 تصویر کے بغیر صحت کا تجزیہ ممکن نہیں ہے۔' :
          '📸 Please upload or take a photo of the animal so that AI can perform the health check.\n\n🔍 Health analysis is not possible without an image.';

        return res.json({
          message: validationMessage,
          requiresImage: true,
          requestType: 'health_check_validation',
          error: false
        });
      }
    }

    // If image is provided, determine if it's an animal or receipt
    if (imageUri) {
      console.log('Processing image analysis...');

      try {
        // First, analyze what type of image this is
        const imageTypeSystemMessage = `You are an expert at analyzing images and understanding user intent based on both image content and text context.
        Analyze the image and the user's text prompt to determine what type of request this is.

        User's text prompt: "${prompt || 'No text provided'}"

        Return ONLY a JSON object with this structure:
        {
          "imageType": "receipt" | "animal" | "animal_health" | "farm" | "milking" | "other",
          "confidence": number (1-100),
          "description": "brief description of what you see",
          "userIntent": "brief description of what user wants to do"
        }

        Image types and user intent analysis:
        - "receipt": Bills, invoices, purchase receipts, payment slips (user wants to track expenses)
        - "animal": Live animals (user wants to ADD a new animal to their farm - look for keywords like "add animal", "new animal", "animal name is")
        - "animal_health": Animal health checks, body parts, feces/poop, health-related images (user wants to record health status)
        - "farm": Farm buildings, barns, fields, agricultural land (user wants to ADD a new farm - look for keywords like "add farm", "farm name is", "create farm")
        - "milking": Milking process, milk containers, milking equipment (user wants to record milking data)
        - "other": Everything else

        IMPORTANT: Pay special attention to the user's text prompt:
        - If text mentions "add this animal", "animal name is", "add animal" → likely "animal"
        - If text mentions "add this farm", "farm name is", "create farm" → likely "farm"
        - If text mentions "health check", "sick", "treatment" → likely "animal_health"
        - If text mentions "milk", "milking", "liters" → likely "milking"
        - If text mentions "expense", "cost", "bill", "receipt" → likely "receipt"`;

        const typeMessages = [
          { role: 'system', content: imageTypeSystemMessage },
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: `Analyze this image and the user's text prompt to determine the request type. User's text: "${prompt || 'No text provided'}"`,
              },
              {
                type: 'image_url',
                image_url: { url: imageUri },
              },
            ],
          }
        ];
        const typeCompletion = await openai.chat.completions.create({
          model: 'gpt-4o',
          messages: typeMessages,
          max_tokens: 300,
          temperature: 0.1,
        });

        const typeResponse = typeCompletion.choices[0]?.message?.content || '';

        let imageTypeData;
        try {
          const jsonMatch = typeResponse.match(/\{[\s\S]*\}/);
          if (jsonMatch) {
            imageTypeData = JSON.parse(jsonMatch[0]);

            // Override image type based on text analysis for better accuracy
            if (requestType === 'add_animal' && imageTypeData.imageType !== 'animal') {
              console.log('🔄 Overriding image type from', imageTypeData.imageType, 'to animal based on text analysis');
              imageTypeData.imageType = 'animal';
              imageTypeData.userIntent = 'User wants to add a new animal to their farm';
            } else if (requestType === 'add_farm' && imageTypeData.imageType !== 'farm') {
              console.log('🔄 Overriding image type from', imageTypeData.imageType, 'to farm based on text analysis');
              imageTypeData.imageType = 'farm';
              imageTypeData.userIntent = 'User wants to add a new farm';
            } else if (requestType === 'health_check' && imageTypeData.imageType !== 'animal_health') {
              console.log('🔄 Overriding image type from', imageTypeData.imageType, 'to animal_health based on text analysis');
              imageTypeData.imageType = 'animal_health';
              imageTypeData.userIntent = 'User wants to record animal health check';
            } else if (requestType === 'expense' && imageTypeData.imageType !== 'receipt') {
              console.log('🔄 Overriding image type from', imageTypeData.imageType, 'to receipt based on text analysis');
              imageTypeData.imageType = 'receipt';
              imageTypeData.userIntent = 'User wants to track an expense';
            }

          }
        } catch (error) {
          console.error('Error parsing image type response:', error);

          // Fallback: Use text-based analysis if image analysis fails
          if (requestType === 'add_animal') {
            imageTypeData = { imageType: 'animal', confidence: 80, description: 'Fallback based on text analysis' };
          } else if (requestType === 'add_farm') {
            imageTypeData = { imageType: 'farm', confidence: 80, description: 'Fallback based on text analysis' };
          } else if (requestType === 'health_check') {
            imageTypeData = { imageType: 'animal_health', confidence: 80, description: 'Fallback based on text analysis' };
          } else if (requestType === 'expense') {
            imageTypeData = { imageType: 'receipt', confidence: 80, description: 'Fallback based on text analysis' };
          } else {
            imageTypeData = { imageType: 'other', confidence: 50, description: 'Unknown type - analysis failed' };
          }

        }

        if (imageTypeData?.imageType === 'animal') {
          // Handle animal data extraction for adding new animals
          const animalExtractionSystemMessage = `You are an expert at extracting animal information from images.
          Analyze this image of an animal and extract relevant data for farm management.

          Return ONLY a JSON object with this structure:
          {
            "isAnimal": true,
            "animalData": {
              "name": "suggested name based on appearance or leave empty",
              "species": "cow|goat|chicken|sheep|buffalo|other",
              "breed": "breed name if identifiable",
              "age": "young|adult|old|unknown",
              "gender": "male|female|unknown",
              "color": "primary color/pattern",
              "weight": "estimated weight in kg or 'unknown'",
              "healthStatus": "healthy|needs_attention|unknown",
              "description": "detailed description of the animal's appearance"
            }
          }`;

          const animalMessages = [
            { role: 'system', content: animalExtractionSystemMessage },
            {
              role: 'user',
              content: [
                {
                  type: 'text',
                  text: 'Extract animal information from this image.',
                },
                {
                  type: 'image_url',
                  image_url: { url: imageUri },
                },
              ],
            }
          ];

          console.log('Calling OpenAI for animal data extraction...');
          const animalCompletion = await openai.chat.completions.create({
            model: 'gpt-4o',
            messages: animalMessages,
            max_tokens: 1000,
            temperature: 0.1,
          });

          try {
            const animalResponse = animalCompletion.choices[0]?.message?.content;
            console.log('Animal extraction response:', animalResponse);

            const animalData = JSON.parse(animalResponse);
            console.log('Parsed animal data:', animalData);

            if (animalData.isAnimal && animalData.animalData) {
              const userMessage = language === 'ur' ?
                `🐄 جانور کی معلومات نکالی گئی

📋 تفصیلات:
• نوع: ${animalData.animalData.species}
• نسل: ${animalData.animalData.breed || 'نامعلوم'}
• عمر: ${animalData.animalData.age}
• جنس: ${animalData.animalData.gender}
• رنگ: ${animalData.animalData.color}
• وزن: ${animalData.animalData.weight}
• صحت: ${animalData.animalData.healthStatus}

📝 تفصیل: ${animalData.animalData.description}

💾 اس جانور کو اپنے فارم میں شامل کرنے کے لیے "ہاں" ٹائپ کریں۔` :
                `🐄 Animal Information Extracted

📋 Details:
• Species: ${animalData.animalData.species}
• Breed: ${animalData.animalData.breed || 'Unknown'}
• Age: ${animalData.animalData.age}
• Gender: ${animalData.animalData.gender}
• Color: ${animalData.animalData.color}
• Weight: ${animalData.animalData.weight}
• Health Status: ${animalData.animalData.healthStatus}

📝 Description: ${animalData.animalData.description}

💾 Type "Yes" to add this animal to your farm.`;

              return res.json({
                message: userMessage,
                animalData: animalData.animalData,
                imageType: 'animal',
                image: imageUri,
                context: {
                  animalData: animalData.animalData,
                  readyToSave: true,
                  imageUri: imageUri
                }
              });
            }
          } catch (error) {
            console.error('Error parsing animal data:', error);
          }
        }

        if (imageTypeData?.imageType === 'animal_health') {
          // Handle animal health analysis
          const healthAnalysisSystemMessage = `You are a veterinary expert analyzing animal health images. 
          Analyze this image for health indicators and provide detailed assessment.

          Return ONLY a JSON object with this structure:
          {
            "isHealthRelated": true,
            "imageContent": "description of what you see (e.g., 'animal feces', 'cow eyes', 'goat legs')",
            "healthAssessment": {
              "appetite": "normal|decreased|increased|none",
              "hydration": "normal|dehydrated|overhydrated", 
              "respiration": "normal|increased|decreased|labored",
              "gait": "normal|limping|stiff|unable",
              "fecal": "normal|diarrhea|constipated|bloody",
              "coat": "normal|dull|patchy|irritated",
              "eyes": "normal|discharge|cloudy|red",
              "ears": "normal|discharge|red|swollen"
            },
            "findings": "detailed analysis of what you observe",
            "recommendations": "suggested next steps or concerns",
            "abnormalities": boolean
          }

          If this is feces/poop, focus on:
          - Consistency (normal, loose, hard, watery)
          - Color (normal, unusual coloring)
          - Presence of blood, mucus, or parasites
          - Overall digestive health indicators

          If this is other body parts, analyze accordingly.`;

          const healthMessages = [
            { role: 'system', content: healthAnalysisSystemMessage },
            {
              role: 'user',
              content: [
                {
                  type: 'text',
                  text: 'Analyze this animal health image and provide detailed health assessment.',
                },
                {
                  type: 'image_url',
                  image_url: { url: imageUri },
                },
              ],
            }
          ];

          console.log('Calling OpenAI for health analysis...');
          const healthCompletion = await openai.chat.completions.create({
            model: 'gpt-4o',
            messages: healthMessages,
            max_tokens: 1000,
            temperature: 0.1,
          });

          const healthResponse = healthCompletion.choices[0]?.message?.content || '';
          console.log('Health analysis response:', healthResponse);

          try {
            const healthJsonMatch = healthResponse.match(/\{[\s\S]*\}/);
            if (healthJsonMatch) {
              const healthData = JSON.parse(healthJsonMatch[0]);

              // Get farm and animal context from request
              let selectedFarm = req.body.context?.selectedFarm;
              let selectedAnimal = req.body.context?.selectedAnimal;
              let availableAnimals = req.body.context?.availableAnimals || [];

              // If no farm context, use the first farm from the farms array
              if (!selectedFarm && req.body.farms && req.body.farms.length > 0) {
                selectedFarm = req.body.farms[0];
                console.log('Using first farm from request:', selectedFarm.name);
              }

              // If we have a farm but no animals, fetch animals for this farm
              if (selectedFarm && availableAnimals.length === 0) {
                try {
                  console.log('Fetching animals for farm:', selectedFarm.id);
                  const farmRef = firestore.collection('farms').doc(selectedFarm.id);
                  const animalsSnapshot = await farmRef.collection('animals').get();

                  availableAnimals = animalsSnapshot.docs.map(doc => ({
                    id: doc.id,
                    name: doc.data().name || 'Unnamed Animal',
                    species: doc.data().species || 'Unknown Species',
                    ...doc.data()
                  }));

                  console.log('Found animals:', availableAnimals.length);
                } catch (error) {
                  console.error('Error fetching animals:', error);
                  availableAnimals = [];
                }
              }

              let contextMessage = '';
              let needsAnimalSelection = false;

              // Check if we have farm context
              if (selectedFarm) {
                contextMessage += language === 'ur' ?
                  `🏡 منتخب فارم: ${selectedFarm.name}\n\n` :
                  `🏡 Selected Farm: ${selectedFarm.name}\n\n`;
              }

              // Check if we need animal selection
              if (!selectedAnimal && availableAnimals.length > 0) {
                // PRIORITY 1: Check if user mentioned a specific animal name in the prompt
                const animalFromName = extractAnimalNameFromPrompt(prompt, availableAnimals);

                if (animalFromName) {
                  // User mentioned a specific animal name - use it
                  selectedAnimal = animalFromName;
                  console.log('🎯 USING EXPLICITLY MENTIONED ANIMAL:', selectedAnimal.name);

                  // Save to session for future use
                  if (userId) {
                    setSelectedAnimal(userId, selectedAnimal);
                  }

                  contextMessage += language === 'ur' ?
                    `🐄 منتخب جانور: ${selectedAnimal.name} (${selectedAnimal.species})\n\n` :
                    `🐄 Selected Animal: ${selectedAnimal.name} (${selectedAnimal.species})\n\n`;
                } else {
                  // PRIORITY 2: Check if we have a previously selected animal in session
                  const sessionAnimal = userId ? getSelectedAnimal(userId) : null;

                  if (sessionAnimal && availableAnimals.find(animal => animal.id === sessionAnimal.id)) {
                    // Use the session-stored animal
                    selectedAnimal = sessionAnimal;
                    console.log('🎯 AUTO-USING SESSION-STORED ANIMAL for health check:', selectedAnimal.name);

                    contextMessage += language === 'ur' ?
                      `🐄 منتخب جانور: ${selectedAnimal.name} (${selectedAnimal.species})\n\n` :
                      `🐄 Selected Animal: ${selectedAnimal.name} (${selectedAnimal.species})\n\n`;
                  } else {
                    // PRIORITY 3: Check if animal type is mentioned in the prompt
                    const animalTypeFromPrompt = extractAnimalTypeFromPrompt(prompt, language);

                  if (animalTypeFromPrompt) {
                    // Filter animals by the mentioned type
                    const filteredAnimals = availableAnimals.filter(animal =>
                      animal.species && animal.species.toLowerCase() === animalTypeFromPrompt.toLowerCase()
                    );

                    if (filteredAnimals.length === 1) {
                      // If only one animal of this type, auto-select it
                      selectedAnimal = filteredAnimals[0];
                      console.log('🎯 AUTO-SELECTED ANIMAL by type:', selectedAnimal.name, selectedAnimal.species);

                      // Save to session for future use
                      if (userId) {
                        setSelectedAnimal(userId, selectedAnimal);
                      }

                      contextMessage += language === 'ur' ?
                        `🐄 منتخب جانور: ${selectedAnimal.name} (${selectedAnimal.species})\n\n` :
                        `🐄 Selected Animal: ${selectedAnimal.name} (${selectedAnimal.species})\n\n`;
                    } else if (filteredAnimals.length > 1) {
                      // Multiple animals of this type, need selection
                      availableAnimals = filteredAnimals;
                      needsAnimalSelection = true;
                    } else {
                      // No animals of this type found, show animal type selection
                      needsAnimalSelection = true;
                    }
                    } else {
                      // No animal type mentioned, need selection
                      needsAnimalSelection = true;
                    }
                  }
                }
              } else if (!selectedAnimal && availableAnimals.length === 0) {
                // No animals available, show animal type selection for health check
                const animalTypeMessage = language === 'ur' ?
                  `🐄 صحت کی جانچ کے لیے جانور کی قسم منتخب کریں:\n\n[گائے] [بکری] [بھیڑ] [بھینس] [دیگر...]` :
                  `🐄 Please select the animal type from the following list to proceed with the health check:\n\n[Cow] [Goat] [Sheep] [Buffalo] [Other...]`;

                return res.json({
                  message: animalTypeMessage,
                  needsAnimalTypeSelection: true,
                  healthData: healthData,
                  imageType: 'animal_health',
                  image: imageUri,
                  context: {
                    healthData,
                    selectedFarm,
                    needsAnimalTypeSelection: true
                  }
                });
              }

              if (needsAnimalSelection) {
                contextMessage += language === 'ur' ?
                  `🐄 جانور کا انتخاب کریں` :
                  `🐄 Select Animal`;

                // Collect all animal images for display
                animalImages = availableAnimals
                  .map(animal => ({
                    imageUri: animal.imageUri,
                    name: animal.name,
                    species: animal.species,
                    id: animal.id
                  }));
              } else if (selectedAnimal) {
                contextMessage += language === 'ur' ?
                  `🐄 منتخب جانور: ${selectedAnimal.name}\n\n` :
                  `🐄 Selected Animal: ${selectedAnimal.name}\n\n`;
              }

              const userMessage = language === 'ur' ?
                `${contextMessage}�� صحت کا تجزیہ مکمل

📋 تصویر کا مواد: ${healthData.imageContent}

🏥 صحت کی تشخیص:
${Object.entries(healthData.healthAssessment).map(([key, value]) =>
                  `• ${key}: ${value}`
                ).join('\n')}

📝 تفصیلی نتائج: ${healthData.findings}

💡 تجاویز: ${healthData.recommendations}

⚠️ غیر معمولی حالت: ${healthData.abnormalities ? 'ہاں' : 'نہیں'}


${needsAnimalSelection ?
                  '🔄 پہلے جانور کا انتخاب کریں، پھر محفوظ کرنے کے لیے "ہاں" ٹائپ کریں۔' :
                  '💾 محفوظ کرنے کے لیے "ہاں" ٹائپ کریں۔'
                }` :
                `${contextMessage}🏥 Health Analysis Complete

📋 Image Content: ${healthData.imageContent}

🏥 Health Assessment:
${Object.entries(healthData.healthAssessment).map(([key, value]) =>
                  `• ${key.charAt(0).toUpperCase() + key.slice(1)}: ${value}`
                ).join('\n')}

📝 Detailed Analysis: ${healthData.findings || 'No specific concerns noted'}
💡 Recommendations: ${healthData.recommendations}

⚠️ Abnormalities Detected: ${healthData.abnormalities ? 'Yes' : 'No'}


${needsAnimalSelection ?
                  '🔄 Please select an animal first, then type "Yes" to save.' :
                  '💾 Type "Yes" to save this health check.'
                }`;
              // Find first animal with image to show as preview
              const firstAnimalWithImage = availableAnimals.find(animal => animal.imageUri);
              const previewImage = needsAnimalSelection && firstAnimalWithImage ? firstAnimalWithImage.imageUri : imageUri;

              return res.json({
                message: userMessage,
                healthData: healthData,
                imageType: 'animal_health',
                readyToSave: !needsAnimalSelection,
                needsAnimalSelection: needsAnimalSelection,
                image: previewImage, // Show first animal image if selecting, otherwise health check image
                animalImages: needsAnimalSelection ? animalImages : undefined, // Send all animal images when selecting
                animalInfo: selectedAnimal ? {
                  name: selectedAnimal.name,
                  species: selectedAnimal.species,
                  id: selectedAnimal.id
                } : null,
                context: {
                  healthData,
                  selectedFarm,
                  selectedAnimal,
                  availableAnimals,
                  needsAnimalSelection,
                  readyToSave: !needsAnimalSelection
                }
              });
            }
          } catch (healthParseError) {
            console.error('Error parsing health analysis:', healthParseError);
            return res.json({
              message: language === 'ur' ?
                'صحت کی جانچ میں خرابی۔ براہ کرم دوبارہ کوشش کریں۔' :
                'Error in health analysis. Please try again.',
              error: true
            });
          }

        } else if (imageTypeData?.imageType === 'receipt') {

          const receiptSystemMessage = `You are an expert at analyzing receipts and bills. Extract all relevant information for expense tracking.

          Return ONLY a JSON object with this structure:
          {
            "isReceipt": true,
            "amount": number (total amount - look for final total, grand total),
            "date": "YYYY-MM-DD" (transaction date if visible),
            "description": "string (detailed description including items, shop name, address)",
            "merchantName": "string (shop/vendor name)",
            "merchantAddress": "string (address if visible)",
            "category": "string (best guess from: ANIMAL_PURCHASE, FEED, MEDICATION, VACCINATION, VETERINARY, EQUIPMENT, UTILITIES, LABOR, MAINTENANCE, OTHER)",
            "paymentMethod": "string (if mentioned: CASH, BANK_TRANSFER, CREDIT_CARD, CHEQUE, MOBILE_PAYMENT, or default to CASH)",
            "currency": "string (PKR, USD, etc. - default to PKR)",
            "items": [
              {
                "name": "string",
                "quantity": number,
                "unitPrice": number,
                "totalPrice": number
              }
            ],
            "confidence": number (1-100)
          }

          Be very accurate with amount extraction - look for totals, grand totals, final amounts.
          For description, include all relevant details like items, quantities, shop info.`;

          const receiptMessages = [
            { role: 'system', content: receiptSystemMessage },
            {
              role: 'user',
              content: [
                {
                  type: 'text',
                  text: 'Extract all expense information from this receipt.',
                },
                {
                  type: 'image_url',
                  image_url: { url: imageUri },
                },
              ],
            }
          ];

          console.log('Calling OpenAI for receipt analysis...');
          const receiptCompletion = await openai.chat.completions.create({
            model: 'gpt-4o',
            messages: receiptMessages,
            max_tokens: 1500,
            temperature: 0.1,
          });

          const receiptResponse = receiptCompletion.choices[0]?.message?.content || '';
          console.log('Receipt analysis response:', receiptResponse);

          try {
            const receiptJsonMatch = receiptResponse.match(/\{[\s\S]*\}/);
            if (receiptJsonMatch) {
              const receiptData = JSON.parse(receiptJsonMatch[0]);
              console.log('Parsed receipt data:', receiptData);

              if (receiptData.isReceipt && userId) {
                const timestamp = Date.now();

                // Parse date or use current date
                let expenseDate = Date.now(); // Default to current timestamp
                if (receiptData.date) {
                  const parsedDate = new Date(receiptData.date);
                  if (!isNaN(parsedDate.getTime()) && parsedDate.getTime() > 0) {
                    expenseDate = parsedDate.getTime();
                  }
                }

                // Ensure expenseDate is valid and not in the future or too far in the past
                const now = Date.now();
                const oneYearAgo = now - (365 * 24 * 60 * 60 * 1000);

                if (expenseDate > now || expenseDate < oneYearAgo) {
                  console.log('Invalid expense date detected, using current date');
                  expenseDate = now;
                }

                // Get farm from dashboard selection (same logic as animal processing)
                const selectedFarmFromDashboard = requestBody.farms;
                let selectedFarm = null;

                if (selectedFarmFromDashboard && selectedFarmFromDashboard.length > 0) {
                  // Use the first farm from the dashboard selection
                  selectedFarm = {
                    id: selectedFarmFromDashboard[0].id,
                    name: selectedFarmFromDashboard[0].name
                  };
                  console.log('Using farm from dashboard for expense:', selectedFarm.name);
                } else {
                  // Fallback: fetch user's farms if no farm data in request
                  try {
                    const farmsSnapshot = await firestore.collection('farms')
                      .where('ownerId', '==', userId)
                      .get();

                    const userFarms = farmsSnapshot.docs.map(doc => ({
                      id: doc.id,
                      ...doc.data()
                    }));

                    selectedFarm = userFarms.find(f => f.name !== 'All farms') || userFarms[0];
                    console.log('Using fallback farm for expense:', selectedFarm?.name);
                  } catch (farmError) {
                    console.error('Error fetching farms for expense:', farmError);
                  }
                }

                if (!selectedFarm) {
                  return res.json({
                    message: language === 'ur' ?
                      'کوئی فارم دستیاب نہیں۔ پہلے فارم بنائیں۔' :
                      'No farms available. Please create a farm first.',
                    error: true
                  });
                }
                // Helper function to map AI categories to proper labels and get IDs
                const mapCategoryData = (aiCategory, defaultIds) => {
                  const categoryMap = {
                    'ANIMAL_PURCHASE': { label: 'Animal Purchase', id: defaultIds.defaultFarmTypeId },
                    'FEED': { label: 'Feed', id: '' },
                    'MEDICATION': { label: 'Medication', id: '' },
                    'VACCINATION': { label: 'Vaccination', id: '' },
                    'VETERINARY': { label: 'Veterinary Service', id: '' },
                    'EQUIPMENT': { label: 'Equipment', id: '' },
                    'UTILITIES': { label: 'Utilities', id: '' },
                    'LABOR': { label: 'Labor', id: '' },
                    'MAINTENANCE': { label: 'Maintenance', id: '' },
                    'OTHER': { label: 'Other', id: '' }
                  };

                  return categoryMap[aiCategory] || { label: 'Other', id: '' };
                };

                // Helper function to map AI payment methods to proper labels and get IDs
                const mapPaymentMethodData = (aiPaymentMethod, defaultIds) => {
                  const paymentMap = {
                    'CASH': { label: 'Cash', id: defaultIds.defaultStatusId },
                    'CARD': { label: 'Card', id: '' },
                    'CREDIT_CARD': { label: 'Card', id: '' },
                    'BANK_TRANSFER': { label: 'Bank Transfer', id: '' },
                    'CHEQUE': { label: 'Bank Transfer', id: '' },
                    'MOBILE_PAYMENT': { label: 'Card', id: '' },
                    'OTHER': { label: 'Cash', id: defaultIds.defaultStatusId }
                  };

                  return paymentMap[aiPaymentMethod] || { label: 'Cash', id: defaultIds.defaultStatusId };
                };

                // Get default IDs from request body (passed from frontend)
                const defaultIds = {
                  defaultFarmTypeId: requestBody.defaultFarmTypeId || "B2bye8PZBQYqscxXoVqZ",
                  defaultStatusId: requestBody.defaultStatusId || "tEbttSFNlpr6gGm5y66l",
                  defaultSizeUnitId: requestBody.defaultSizeUnitId || "QmzgdLcdPP0iEFVT5LyP"
                };

                // Map AI extracted data to proper format
                const categoryData = mapCategoryData(receiptData.category || 'OTHER', defaultIds);
                const paymentMethodData = mapPaymentMethodData(receiptData.paymentMethod || 'CASH', defaultIds);

                // Create expense data matching add.tsx structure
                const completeExpenseData = {
                  amount: receiptData.amount || 0,
                  currency: 'PKR',//receiptData.currency || 'PKR',
                  date: expenseDate, // Use validated timestamp
                  category: categoryData.label, // Use proper label format
                  categoryId: categoryData.id, // Use mapped ID
                  description: formatExpenseDescription(receiptData),
                  farmId: selectedFarm.id,
                  farmName: selectedFarm.name,
                  animalId: '', // Default empty
                  animalName: '', // Default empty
                  paymentMethod: paymentMethodData.label, // Use proper label format
                  paymentMethodId: paymentMethodData.id, // Use mapped ID
                  receiptImage: imageUri,
                  createdBy: userId,
                  tenantId: userId,
                  createdAt: new Date(now).toLocaleString('en-US', {
                    timeZone: 'Asia/Karachi',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: 'numeric',
                    minute: 'numeric',
                    hour12: true
                  }),
                  createdAtTimestamp: now, // Use current time for creation
                  updatedAt: new Date(now).toLocaleString('en-US', {
                    timeZone: 'Asia/Karachi',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: 'numeric',
                    minute: 'numeric',
                    hour12: true
                  }),
                  updatedAtTimestamp: now, // Use current time for update
                  dashboardFarm: selectedFarm.name // For debugging
                };

                // Helper function to format expense description

                try {
                  // Save expense to database
                  const savedExpense = await saveExpenseToDatabase(completeExpenseData);

                  const userMessage = language === 'ur' ?
                    `🧾 خرچہ کامیابی سے محفوظ ہو گیا:
                    
رقم: ${receiptData.currency || 'PKR'} ${receiptData.amount}
تاریخ: ${new Date(expenseDate).toLocaleDateString()}
کیٹگری: ${categoryData.label}
ادائیگی: ${paymentMethodData.label}
فارم: ${selectedFarm.name}
🆔 ڈیٹابیس ID: ${savedExpense.id}

✅ کامیابی سے محفوظ ہو گیا!` :

                    `🧾 Expense Saved Successfully:
                    
Amount: ${receiptData.currency || 'PKR'} ${receiptData.amount}
Date: ${new Date(expenseDate).toLocaleDateString()}
Category: ${categoryData.label}
Payment: ${paymentMethodData.label}
Farm: ${selectedFarm.name}
🆔 Database ID: ${savedExpense.id}

✅ Successfully saved to database!`;

                  return res.json({
                    message: userMessage,
                    expenseData: savedExpense,
                    saved: true,
                    databaseId: savedExpense.id,
                    imageType: 'receipt'
                  });
                } catch (saveError) {
                  console.error('Error saving expense:', saveError);
                  return res.json({
                    message: language === 'ur' ?
                      'ڈیٹابیس میں محفوظ کرنے میں خرابی۔ براہ کرم دوبارہ کوشش کریں۔' :
                      'Error saving to database. Please try again.',
                    error: true
                  });
                }
              }
            }
          } catch (parseError) {
            console.error('Error parsing receipt response:', parseError);
            return res.json({
              message: language === 'ur' ?
                'رسید کا تجزیہ ناکام۔ براہ کرم دوبارہ کوشش کریں۔' :
                'Receipt analysis failed. Please try again.',
              error: true
            });
          }

        } else if (imageTypeData?.imageType === 'animal') {
          console.log('Processing as animal...');

          const animalSystemMessage = `You are an expert veterinarian and animal identification specialist. Analyze animal images and extract structured data.
          Return ONLY a JSON object with the following structure:
          {
            "isAnimal": boolean,
            "species": "Cow" | "Goat" | "Sheep" | "Horse" | "Buffalo" | "Camel" | "Donkey" | "Pig" | "Dog" | "Cat" | "Poultry" | "Fish" | "Rabbit",
            "breed": "string (specific breed name or 'Unknown')",
            "gender": "male" | "female" | "unknown",
            "estimatedAge": number (in years),
            "estimatedWeight": number (in kg),
            "confidence": number (1-100),
            "healthStatus": "excellent" | "good" | "fair" | "poor" | "critical",
            "bodyCondition": "thin" | "normal" | "overweight" | "obese",
            "visibleIssues": ["injury", "disease", "parasites", "malnutrition", "etc"],
            "colorMarkings": "detailed description of color and markings"
          }`;

          const animalMessages = [
            { role: 'system', content: animalSystemMessage },
            {
              role: 'user',
              content: [
                {
                  type: 'text',
                  text: 'Analyze this animal image and return structured data as JSON.',
                },
                {
                  type: 'image_url',
                  image_url: { url: imageUri },
                },
              ],
            }
          ];

          console.log('Calling OpenAI for animal analysis...');
          const animalCompletion = await openai.chat.completions.create({
            model: 'gpt-4o',
            messages: animalMessages,
            max_tokens: 1000,
            temperature: 0.1,
          });

          const animalResponse = animalCompletion.choices[0]?.message?.content || '';
          console.log('Animal analysis response:', animalResponse);

          try {
            const animalJsonMatch = animalResponse.match(/\{[\s\S]*\}/);
            if (animalJsonMatch) {
              const animalData = JSON.parse(animalJsonMatch[0]);
              console.log('Parsed animal data:', animalData);

              if (animalData.isAnimal && userId) {
                // Use AI to extract information from prompt
                const extractedInfo = await analyzePromptWithAI(prompt, 'animal');

                console.log('AI extracted info:', extractedInfo);

                // Get farm selection from request body (from dashboard)
                const selectedFarmFromDashboard = requestBody.farms; // Array of farms from frontend
                let selectedFarm = null;

                if (selectedFarmFromDashboard && selectedFarmFromDashboard.length > 0) {
                  // Use the first farm from the dashboard selection
                  // If "All" was selected, frontend sends all farms and we use the first one
                  selectedFarm = {
                    id: selectedFarmFromDashboard[0].id,
                    name: selectedFarmFromDashboard[0].name,
                    location: selectedFarmFromDashboard[0].location
                  };
                  console.log('Using farm from dashboard selection:', selectedFarm.name);
                } else {
                  // Fallback: fetch user's farms if no farm data in request
                  try {
                    const farmsSnapshot = await firestore.collection('farms')
                      .where('ownerId', '==', userId)
                      .get();

                    const userFarms = farmsSnapshot.docs.map(doc => ({
                      id: doc.id,
                      ...doc.data()
                    }));

                    selectedFarm = userFarms[0] || {
                      id: 'default_farm',
                      name: 'Default Farm'
                    };
                    console.log('Using fallback farm:', selectedFarm.name);
                  } catch (farmError) {
                    console.error('Error fetching farms:', farmError);
                    selectedFarm = {
                      id: 'default_farm',
                      name: 'Default Farm'
                    };
                  }
                }

                // Use AI-extracted animal name or generate auto name
                const animalName = (extractedInfo && extractedInfo.animalName) || `${animalData.species}${Date.now().toString().slice(-4)}`;
                const autoTagId = `${animalData.species.substring(0, 3).toUpperCase()}${Date.now().toString().slice(-6)}`;

                // Create complete animal data
                const timestamp = Date.now();
                const completeAnimalData = {
                  name: animalName,
                  species: animalData.species,
                  breed: animalData.breed || 'Unknown',
                  speciesId: null,
                  breedId: null,
                  age: animalData.estimatedAge || 0,
                  weight: animalData.estimatedWeight || 0,
                  gender: animalData.gender === 'male' || animalData.gender === 'female' ? animalData.gender : 'female',
                  tagId: autoTagId,
                  birthDate: timestamp - (animalData.estimatedAge * 365 * 24 * 60 * 60 * 1000),
                  ownerId: userId,
                  tenantId: userId,
                  farmId: selectedFarm.id,
                  fatherId: "BOUGHT_EXTERNAL",
                  motherId: "BOUGHT_EXTERNAL",
                  hasHealthIssues: animalData.visibleIssues?.length > 0 || false,
                  healthCheckInterval: 7,
                  nextHealthCheck: timestamp + (7 * 24 * 60 * 60 * 1000),
                  imageUri: imageUri,
                  healthStatus: animalData.healthStatus,
                  bodyCondition: animalData.bodyCondition,
                  colorMarkings: animalData.colorMarkings,
                  analysisConfidence: animalData.confidence,
                  pregnancyStatus: "not_pregnant",
                  extractedInfo: extractedInfo,
                  dashboardFarm: selectedFarm.name // For debugging
                };

                // Store for potential name changes
                previousAnimalData = completeAnimalData;

                const userMessage = language === 'ur' ?
                  `🐄 جانور کی تفصیلات:

نام: ${animalName}
قسم: ${animalData.species}
نسل: ${animalData.breed || 'نامعلوم'}
جنس: ${animalData.gender === 'male' ? 'نر' : 'مادہ'}
عمر: ${animalData.estimatedAge} سال
وزن: ${animalData.estimatedWeight} کلو
صحت: ${animalData.healthStatus}
ٹیگ: ${completeAnimalData.tagId}
فارم: ${selectedFarm.name} (ڈیش بورڈ سے منتخب)

اعتماد: ${animalData.confidence}%
AI اعتماد: ${(extractedInfo && extractedInfo.confidence) || 'N/A'}%

💡 اگلے قدم:
• نام تبدیل کرنے کے لیے: "نام تبدیل کر کے [نیا نام] رکھیں"
• محفوظ کرنے کے لیے: "محفوظ کریں" یا "save"` :

                  `🐄 Animal Analysis Results:

Name: ${animalName}
Species: ${animalData.species}
Breed: ${animalData.breed || 'Unknown'}
Gender: ${animalData.gender}
Age: ${animalData.estimatedAge} years
Weight: ${animalData.estimatedWeight} kg
Health: ${animalData.healthStatus}
Tag ID: ${completeAnimalData.tagId}
Farm: ${selectedFarm.name} (from dashboard)

Confidence: ${animalData.confidence}%
AI Confidence: ${(extractedInfo && extractedInfo.confidence) || 'N/A'}%

💡 Next Steps:
• To change name: "change name to [new name]"
• To save: "save" or "add to database"`;

                return res.json({
                  message: userMessage,
                  animalData: completeAnimalData,
                  analysisData: animalData,
                  extractedInfo: extractedInfo,
                  imageType: 'animal',
                  readyToSave: true
                });
              } else {
                // Not an animal
                const errorMessage = language === 'ur' ?
                  'یہ تصویر میں جانور نظر نہیں آ رہا۔ براہ کرم جانور کی واضح تصویر اپ لوڈ کریں۔' :
                  'No animal detected in this image. Please upload a clear animal photo.';

                return res.json({
                  message: errorMessage,
                  imageType: 'animal',
                  error: true
                });
              }
            }
          } catch (parseError) {
            console.error('Error parsing animal response:', parseError);
            return res.json({
              message: language === 'ur' ?
                'جانور کا تجزیہ ناکام۔ براہ کرم دوبارہ کوشش کریں۔' :
                'Animal analysis failed. Please try again.',
              error: true
            });
          }

        } else if (imageTypeData?.imageType === 'farm') {
          console.log('Processing as farm...');

          // Use AI to extract farm information from prompt
          const extractedInfo = await analyzePromptWithAI(prompt, 'farm');
          console.log('🔍 Text extraction results:', JSON.stringify(extractedInfo, null, 2));

          // Get selected farm from dashboard for location context
          const selectedFarmFromDashboard = requestBody.farms;
          let farmLocation = requestBody.farmLocation || "";

          if (selectedFarmFromDashboard && selectedFarmFromDashboard.length > 0) {
            const dashboardFarm = selectedFarmFromDashboard[0];
            if (dashboardFarm.location) {
              farmLocation = typeof dashboardFarm.location === 'string'
                ? dashboardFarm.location
                : dashboardFarm.location.address || "";
            }
            console.log('Using location context from dashboard farm:', farmLocation);
          }

          // Continue with existing farm analysis...
          const farmSystemMessage = `You are an expert at analyzing farm images and extracting structured data.
          
          Return ONLY a JSON object with this structure:
          {
            "isFarm": boolean,
            "farmType": "livestock" | "crop" | "dairy" | "poultry" | "mixed" | "other",
            "estimatedSize": number (in acres),
            "sizeUnit": "acre" | "hectare" | "square_meter",
            "structures": ["house", "barn", "shed", "fence", "water_source", "etc"],
            "landType": "flat" | "hilly" | "mixed",
            "vegetation": "grass" | "crops" | "trees" | "mixed" | "barren",
            "confidence": number (1-100),
            "description": "detailed description of the farm",
          }`;

          const farmMessages = [
            { role: 'system', content: farmSystemMessage },
            {
              role: 'user',
              content: [
                {
                  type: 'text',
                  text: `Analyze this farm image. Context: ${farmLocation || 'No location provided'}`,
                },
                {
                  type: 'image_url',
                  image_url: { url: imageUri },
                },
              ],
            }
          ];

          console.log('Calling OpenAI for farm analysis...');
          const farmCompletion = await openai.chat.completions.create({
            model: 'gpt-4o',
            messages: farmMessages,
            max_tokens: 1000,
            temperature: 0.1,
          });

          const farmResponse = farmCompletion.choices[0]?.message?.content || '';
          console.log('Farm analysis response:', farmResponse);

          try {
            const farmJsonMatch = farmResponse.match(/\{[\s\S]*\}/);
            if (farmJsonMatch) {
              const farmData = JSON.parse(farmJsonMatch[0]);
              console.log('🖼️ Image analysis results:', JSON.stringify(farmData, null, 2));
              console.log('🔄 Comparing: Text farmType =', (extractedInfo && extractedInfo.farmType), 'vs Image farmType =', farmData.farmType);

              if (farmData.isFarm) {
                // Use AI-extracted farm name or generate auto name
                const farmName = (extractedInfo && extractedInfo.farmName) || `Farm${Date.now().toString().slice(-4)}`;

                const completeFarmData = {
                  name: farmName,
                  farmType: (extractedInfo && extractedInfo.farmType) || farmData.farmType || 'livestock', // Prioritize text extraction
                  estimatedSize: (extractedInfo && extractedInfo.farmSize) || farmData.estimatedSize || 50,
                  sizeUnit: (extractedInfo && extractedInfo.sizeUnit) || farmData.sizeUnit || 'acre',
                  structures: farmData.structures || ['house', 'shed', 'fence'],
                  landType: farmData.landType,
                  vegetation: farmData.vegetation,
                  description: farmData.description,
                  imageUri: imageUri,
                  analysisConfidence: farmData.confidence,
                  readyToSave: true,
                  extractedInfo: extractedInfo,
                  dashboardLocation: (extractedInfo && extractedInfo.farmLocation) || farmLocation // Prioritize extracted location
                };

                previousFarmData = completeFarmData;

                // Build display message with only meaningful data (hide defaults)
                let displayParts = [];

                // Always show name and confidence
                displayParts.push(`Name: ${farmName}`);

                // Show type if extracted from text or if it's meaningful from image analysis
                const displayType = (extractedInfo && extractedInfo.farmType) || farmData.farmType;
                if (displayType) {
                  displayParts.push(`Type: ${displayType}`);
                }

                // Show size with extracted or analyzed values
                const displaySize = (extractedInfo && extractedInfo.farmSize) || farmData.estimatedSize;
                const displayUnit = (extractedInfo && extractedInfo.sizeUnit) || farmData.sizeUnit;
                if (displaySize && displaySize !== 50) {
                  displayParts.push(`Size: ${displaySize} ${displayUnit || 'acre'}`);
                }

                // Only show location if we have meaningful data
                const displayLocation = (extractedInfo && extractedInfo.farmLocation) || farmLocation;
                if (displayLocation && displayLocation !== 'from dashboard' && displayLocation !== 'ڈیش بورڈ سے') {
                  displayParts.push(`Location: ${displayLocation}`);
                }

                // Don't show features at all - removed as requested

                displayParts.push(`Confidence: ${farmData.confidence}%`);

                const userMessage = language === 'ur' ?
                  `🏡 فارم کی تفصیلات:

${displayParts.join('\n')}

💡 اگلے قدم:
• نام تبدیل کرنے کے لیے: "نام تبدیل کر کے [نیا نام] رکھیں"
• پتہ تبدیل کرنے کے لیے: "پتہ تبدیل کرو [نیا پتہ]"
• قسم تبدیل کرنے کے لیے: "قسم تبدیل کرو [نئی قسم]"
• سائز تبدیل کرنے کے لیے: "سائز تبدیل کرو [نیا سائز]"
• محفوظ کرنے کے لیے: "محفوظ کریں"` :

                  `🏡 Farm Analysis Results:

${displayParts.join('\n')}

💡 Next Steps:
• To change name: "change name to [new name]"
• To change address: "change address to [new address]"
• To change type: "change type to [new type]"
• To change size: "change size to [new size]"
• To save: "save"`;

                return res.json({
                  message: userMessage,
                  farmData: completeFarmData,
                  analysisData: farmData,
                  extractedInfo: extractedInfo,
                  imageType: 'farm',
                  readyToSave: true
                });
              }
            }
          } catch (parseError) {
            console.error('Error parsing farm response:', parseError);
            return res.json({
              message: language === 'ur' ?
                'فارم کا تجزیہ ناکام۔ براہ کرم دوبارہ کوشش کریں۔' :
                'Farm analysis failed. Please try again.',
              error: true
            });
          }

        } else {
          console.log('Image type: other');
          const errorMessage = language === 'ur' ?
            'یہ تصویر نہ تو جانور کی ہے اور نہ ہی رسید کی۔ براہ کرم جانور کی تصویر یا رسید اپ لوڈ کریں۔' :
            'This image is neither an animal nor a receipt. Please upload an animal photo or receipt image.';

          return res.json({
            message: errorMessage,
            imageType: 'other',
            error: true
          });
        }
      } catch (imageError) {
        console.error('Error in image processing:', imageError);
        return res.json({
          message: language === 'ur' ?
            'تصویر کا تجزیہ ناکام۔ براہ کرم دوبارہ کوشش کریں۔' :
            'Image analysis failed. Please try again.',


          error: true
        });
      }
    }

    // Generic animal selection handler (excludes specific contexts like deletion and editing)
    // IMPORTANT: Only run this if it's NOT a "yes" response for saving
    const userLowerMessage = prompt.toLowerCase();
    if (req.body.context?.needsAnimalSelection &&
      req.body.context?.action !== 'delete_animal' &&
      req.body.context?.action !== 'delete_expense' &&
      req.body.context?.action !== 'edit_animal' &&
      req.body.context?.action !== 'edit_animal_fields' &&
      !req.body.context?.milkingData &&
      !req.body.context?.needsExpenseSelection &&
      !userLowerMessage.includes('yes') &&
      !userLowerMessage.includes('ہاں') &&
      !req.body.context?.readyToSave) {
      const userInput = prompt.trim();
      const availableAnimals = req.body.context?.availableAnimals || [];
      let selectedAnimal = null;

      // First, check if we have a previously selected animal in session
      const userId = req.body.userId;
      const sessionAnimal = userId ? getSelectedAnimal(userId) : null;

      // If we have a session animal and it's in the available animals list, use it
      if (sessionAnimal && availableAnimals.find(animal => animal.id === sessionAnimal.id)) {
        selectedAnimal = sessionAnimal;
        console.log('🎯 USING SESSION-STORED ANIMAL for generic selection:', selectedAnimal.name);
      } else {
        // Try to find by ID first (for clickable selection)
        selectedAnimal = availableAnimals.find(animal => animal.id === userInput);

        if (!selectedAnimal && /^\d+$/.test(userInput)) {
          // Try to find by number
          const animalIndex = parseInt(userInput) - 1;
          if (animalIndex >= 0 && animalIndex < availableAnimals.length) {
            selectedAnimal = availableAnimals[animalIndex];
          }
        }

        // Save the selected animal to session for future use
        if (selectedAnimal && userId) {
          setSelectedAnimal(userId, selectedAnimal);
        }
      }

      if (selectedAnimal) {
        const { healthData, selectedFarm, healthRecordData, milkingData, animalData } = req.body.context;

        // Check if this might be a health record context (even if data is missing)
        const isHealthRecord = !!healthRecordData || (!healthData && !milkingData && !animalData);

        console.log('🔍 GENERIC HANDLER CONTEXT CHECK:', {
          hasHealthData: !!healthData,
          hasHealthRecordData: !!healthRecordData,
          hasMilkingData: !!milkingData,
          hasAnimalData: !!animalData,
          isHealthRecord: isHealthRecord
        });

        // For health records, create complete record data
        let contextToReturn = {
          selectedFarm,
          selectedAnimal: selectedAnimal
        };

        if (isHealthRecord && healthRecordData) {
          // Health record flow - preserve health record data
          const currentDate = new Date();
          const completeRecordData = {
            ...healthRecordData,
            farmId: selectedFarm.id,
            farmName: selectedFarm.name,
            animalId: selectedAnimal.id,
            animalName: selectedAnimal.name,
            date: healthRecordData.date ? new Date(healthRecordData.date).toISOString() : currentDate.toISOString(),
            createdBy: req.body.userId,
            createdAt: currentDate.toISOString(),
            updatedAt: currentDate.toISOString()
          };

          contextToReturn = {
            healthRecordData: completeRecordData,
            selectedFarm,
            selectedAnimal,
            readyToSave: true
          };

          console.log('🏥 PRESERVING HEALTH RECORD CONTEXT:', {
            recordType: completeRecordData.recordType,
            recordOption: completeRecordData.recordOption,
            animalName: completeRecordData.animalName,
            farmName: completeRecordData.farmName
          });
        } else {
          // Other flows (health check, milking, etc.)
          contextToReturn = {
            healthData,
            milkingData,
            animalData,
            selectedFarm,
            selectedAnimal: selectedAnimal,
            readyToSave: true
          };
        }

        const confirmMessage = language === 'ur' ?
          `✅ جانور منتخب ہو گیا: ${selectedAnimal.name}

🏡 فارم: ${selectedFarm?.name || 'نامعلوم فارم'}
🐄 جانور: ${selectedAnimal.name} (${selectedAnimal.species})

💾 کیا آپ یہ ${isHealthRecord ? 'صحت کا ریکارڈ' : 'صحت کی جانچ'} محفوظ کرنا چاہتے ہیں؟ "ہاں" ٹائپ کریں۔` :
          `✅ Animal Selected: ${selectedAnimal.name}

🏡 Farm: ${selectedFarm?.name || 'Unknown Farm'}
🐄 Animal: ${selectedAnimal.name} (${selectedAnimal.species})

💾 Would you like to save this ${isHealthRecord ? 'health record' : 'health check'}? Type "Yes" to confirm.`;

        return res.json({
          message: confirmMessage,
          image: selectedAnimal.imageUri || undefined, // Show the selected animal's picture
          animalInfo: {
            name: selectedAnimal.name,
            species: selectedAnimal.species,
            id: selectedAnimal.id
          },
          context: contextToReturn
        });
      } else {
        const errorMessage = language === 'ur' ?
          '❌ غلط نمبر۔ براہ کرم فہرست سے صحیح نمبر منتخب کریں۔' :
          '❌ Invalid number. Please select a valid number from the list.';

        return res.json({ message: errorMessage });
      }
    }

    // Handle animal selection for milking (must come before health check handler)
    if (req.body.context?.needsAnimalSelection && req.body.context?.milkingData && req.body.context?.availableAnimals) {
      const { availableAnimals, milkingData, selectedFarm } = req.body.context;

      // Check if user provided an animal ID, number, or animal name
      const selection = prompt.trim();
      let selectedAnimal = null;

      // Try to find by ID first (for clickable selection)
      selectedAnimal = availableAnimals.find(animal => animal.id === selection);
      if (selectedAnimal) {
        console.log('Selected animal by ID:', selectedAnimal.name);
      } else {
        // Try to parse as number
        const animalIndex = parseInt(selection) - 1;
        if (!isNaN(animalIndex) && animalIndex >= 0 && animalIndex < availableAnimals.length) {
          selectedAnimal = availableAnimals[animalIndex];
          console.log('Selected animal by index:', selectedAnimal.name);
        } else {
          // Try to find by name
          selectedAnimal = availableAnimals.find(animal =>
            animal.name.toLowerCase().includes(selection.toLowerCase())
          );
          console.log('Selected animal by name:', selectedAnimal?.name);
        }
      }

      if (selectedAnimal) {
        // Prepare milking data with selected animal
        const completeMilkingData = {
          farmId: selectedFarm.id,
          farmName: selectedFarm.name,
          animalId: selectedAnimal.id,
          animalName: selectedAnimal.name,
          animalSpecies: selectedAnimal.species,
          date: Date.now(),
          session: milkingData.session,
          quantity: parseFloat(milkingData.quantity),
          quality: milkingData.quality || 'good',
          fat: milkingData.fat ? parseFloat(milkingData.fat) : undefined,
          protein: milkingData.protein ? parseFloat(milkingData.protein) : undefined,
          temperature: milkingData.temperature ? parseFloat(milkingData.temperature) : undefined,
          notes: milkingData.notes || undefined,
          createdBy: userId,
          tenantId: userId
        };

        const confirmationMessage = language === 'ur' ?
          `📋 دودھ کی تفصیلات

🐄 جانور: ${selectedAnimal.name} (${selectedAnimal.species})
⏰ وقت: ${milkingData.session}
🥛 مقدار: ${milkingData.quantity} لیٹر
⭐ کوالٹی: ${milkingData.quality || 'اچھی'}
${milkingData.fat ? `🧈 چربی: ${milkingData.fat}%\n` : ''}${milkingData.protein ? `🥩 پروٹین: ${milkingData.protein}%\n` : ''}${milkingData.temperature ? `🌡️ درجہ حرارت: ${milkingData.temperature}°C\n` : ''}${milkingData.notes ? `📝 نوٹس: ${milkingData.notes}\n` : ''}
کیا آپ یہ ڈیٹا محفوظ کرنا چاہتے ہیں؟ "Yes" ٹائپ کریں۔` :
          `📋 Milking Record Details

🐄 Animal: ${selectedAnimal.name} (${selectedAnimal.species})
⏰ Session: ${milkingData.session}
🥛 Quantity: ${milkingData.quantity} liters
⭐ Quality: ${milkingData.quality || 'good'}
${milkingData.fat ? `🧈 Fat: ${milkingData.fat}%\n` : ''}${milkingData.protein ? `🥩 Protein: ${milkingData.protein}%\n` : ''}${milkingData.temperature ? `🌡️ Temperature: ${milkingData.temperature}°C\n` : ''}${milkingData.notes ? `📝 Notes: ${milkingData.notes}\n` : ''}
Do you want to save this milking record? Type "Yes"  to confirm.`;

        return res.json({
          message: confirmationMessage,
          image: selectedAnimal.imageUri || undefined, // Show the selected animal's picture
          animalInfo: {
            name: selectedAnimal.name,
            species: selectedAnimal.species,
            id: selectedAnimal.id
          },
          context: {
            milkingData: completeMilkingData,
            selectedFarm,
            selectedAnimal,
            readyToSave: true,
            needsAnimalSelection: false
          }
        });
      } else {
        const errorMessage = language === 'ur' ?
          `❌  غلط انتخاب  - براہ کرم کوئی جانور منتخب کریں۔` :
          `❌  Invalid Selection  - Please select an animal.`;

        // Collect all animal images to show in the error message
        const animalImages = availableAnimals
          .map(animal => ({
            imageUri: animal.imageUri,
            name: animal.name,
            species: animal.species,
            id: animal.id
          }));

        return res.json({
          message: errorMessage,
          animalImages: animalImages, // Send all animal images
          context: req.body.context
        });
      }
    }

    // Handle animal selection for health check
    if (req.body.context?.needsAnimalSelection && req.body.context?.healthData && req.body.context?.availableAnimals) {
      console.log('🏥 HEALTH CHECK HANDLER TRIGGERED');
      const { healthData, selectedFarm, availableAnimals } = req.body.context;
      const userInput = prompt.trim();

      // First, check if we have a previously selected animal in session
      const userId = req.body.userId;
      const sessionAnimal = userId ? getSelectedAnimal(userId) : null;

      let selectedAnimal = null;

      // If we have a session animal and it's in the available animals list, use it
      if (sessionAnimal && availableAnimals.find(animal => animal.id === sessionAnimal.id)) {
        selectedAnimal = sessionAnimal;
        console.log('🎯 USING SESSION-STORED ANIMAL for health check:', selectedAnimal.name);
      } else {
        // Try to match animal by ID, number, or name
        // Try to find by ID first (for clickable selection)
        selectedAnimal = availableAnimals.find(animal => animal.id === userInput);
        if (!selectedAnimal) {
          // Check if input is a number
          const animalIndex = parseInt(userInput) - 1;
          if (!isNaN(animalIndex) && animalIndex >= 0 && animalIndex < availableAnimals.length) {
            selectedAnimal = availableAnimals[animalIndex];
          } else {
            // Try to match by name (case insensitive)
            selectedAnimal = availableAnimals.find(animal =>
              animal.name.toLowerCase().includes(userInput.toLowerCase())
            );
          }
        }

        // Save the selected animal to session for future use
        if (selectedAnimal && userId) {
          setSelectedAnimal(userId, selectedAnimal);
        }
      }

      if (selectedAnimal) {
        const confirmationMessage = language === 'ur' ?
          `✅  جانور منتخب کیا گیا : ${selectedAnimal.name} (${selectedAnimal.species})

🏥  صحت کا تجزیہ :
📋  تصویر کا مواد : ${healthData.imageContent}

📝  تفصیلی نتائج : ${healthData.findings}
💡  تجاویز : ${healthData.recommendations}
⚠️  غیر معمولی حالت : ${healthData.abnormalities ? 'ہاں' : 'نہیں'}

💾  محفوظ کرنے کے لیے "ہاں" ٹائپ کریں۔ ` :
          `✅  Animal Selected : ${selectedAnimal.name} (${selectedAnimal.species})

🏥  Health Analysis :
📋  Image Content : ${healthData.imageContent}

📝  Detailed Analysis : ${healthData.findings}
💡  Recommendations : ${healthData.recommendations}
⚠️  Abnormalities Detected : ${healthData.abnormalities ? 'Yes' : 'No'}

💾  Type "Yes" to save this health check. `;

        return res.json({
          message: confirmationMessage,
          image: selectedAnimal.imageUri || undefined, // Show the selected animal's picture
          animalInfo: {
            name: selectedAnimal.name,
            species: selectedAnimal.species,
            id: selectedAnimal.id
          },
          context: {
            healthData,
            selectedFarm,
            selectedAnimal,
            availableAnimals,
            readyToSave: true,
            needsAnimalSelection: false
          }
        });
      } else {


        const errorMessage = language === 'ur' ?
          `❌  غلط انتخاب  - براہ کرم کوئی جانور منتخب کریں۔` :
          `❌  Invalid Selection  - Please select an animal.`;

        // Collect all animal images to show in the error message
        const animalImages = availableAnimals
          .map(animal => ({
            imageUri: animal.imageUri,
            name: animal.name,
            species: animal.species,
            id: animal.id
          }));

        return res.json({
          message: errorMessage,
          animalImages: animalImages, // Send all animal images
          context: req.body.context
        });
      }
    }

    // Handle animal selection for deletion
    if (req.body.context?.needsAnimalSelection && req.body.context?.action === 'delete_animal') {
      console.log('🗑️ ANIMAL DELETION SELECTION HANDLER TRIGGERED');
      const { farmId, farmName, reason } = req.body.context;
      const userInput = prompt.trim();

      // Get all animals from the farm for selection
      const farmRef = firestore.collection('farms').doc(farmId);
      const animalsSnapshot = await farmRef.collection('animals').get();
      const availableAnimals = animalsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      // Try to match animal by ID (when user clicks on animal image, the animal ID is sent)
      let selectedAnimal = availableAnimals.find(animal => animal.id === userInput);

      if (selectedAnimal) {
        console.log('Animal selected for deletion:', selectedAnimal.name);

        try {
          // Delete the animal directly without confirmation as per requirements
          const deletedAnimal = await deleteAnimalFromDatabase(
            selectedAnimal.id,
            farmId,
            reason
          );

          const successMessage = language === 'ur' ?
            `✅ جانور کامیابی سے حذف کر دیا گیا:

نام: ${deletedAnimal.name}
نوع: ${deletedAnimal.species}
🆔 ڈیٹابیس ID: ${deletedAnimal.id}
${reason ? `وجہ: ${reason}` : ''}

⚠️ یہ عمل واپس نہیں ہو سکتا۔` :
            `✅ Animal Successfully Deleted:

Name: ${deletedAnimal.name}
Species: ${deletedAnimal.species}
🆔 Database ID: ${deletedAnimal.id}
${reason ? `Reason: ${reason}` : ''}

⚠️ This action cannot be undone.`;

          return res.json({
            message: successMessage,
            animalData: deletedAnimal,
            deleted: true,
            databaseId: deletedAnimal.id
          });
        } catch (deleteError) {
          console.error('Error deleting animal:', deleteError);
          return res.json({
            message: language === 'ur' ?
              'جانور حذف کرنے میں خرابی۔ براہ کرم دوبارہ کوشش کریں۔' :
              'Error deleting animal. Please try again.',
            error: true
          });
        }
      } else {
        // Invalid selection - show animal list again
        const errorMessage = language === 'ur' ?
          `❌ غلط انتخاب - براہ کرم کوئی جانور منتخب کریں۔` :
          `❌ Invalid Selection - Please select an animal.`;

        // Collect all animal images to show in the error message
        const animalImages = availableAnimals.map(animal => ({
          imageUri: animal.imageUri || null,
          name: animal.name,
          species: animal.species,
          id: animal.id
        }));

        return res.json({
          message: errorMessage,
          animalImages: animalImages,
          context: req.body.context
        });
      }
    }

    // Handle expense selection for deletion
    if (req.body.context?.needsExpenseSelection && req.body.context?.action === 'delete_expense') {
      console.log('🗑️ EXPENSE DELETION SELECTION HANDLER TRIGGERED');
      const { farmId, farmName, reason } = req.body.context;
      const userInput = prompt.trim();

      // Get all expenses from the farm for selection
      const farmRef = firestore.collection('farms').doc(farmId);
      const expensesSnapshot = await farmRef.collection('expenses').get();
      const availableExpenses = expensesSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      // Try to match expense by ID (when user clicks on expense image, the expense ID is sent)
      let selectedExpense = availableExpenses.find(expense => expense.id === userInput);

      if (selectedExpense) {
        console.log('Expense selected for deletion:', selectedExpense.category);

        try {
          // Delete the expense directly without confirmation as per requirements
          const deletedExpense = await deleteExpenseFromDatabase(
            selectedExpense.id,
            farmId,
            reason
          );

          const successMessage = language === 'ur' ?
            `✅ اخراجات کامیابی سے حذف کر دیا گیا:

نوع: ${deletedExpense.category || 'Unknown'}
رقم: ${deletedExpense.currency || 'PKR'} ${(deletedExpense.amount || 0).toLocaleString()}
تفصیل: ${deletedExpense.description || 'No description'}
🆔 ڈیٹابیس ID: ${deletedExpense.id}
${reason ? `وجہ: ${reason}` : ''}

⚠️ یہ عمل واپس نہیں ہو سکتا۔` :
            `✅ Expense Successfully Deleted:

Category: ${deletedExpense.category || 'Unknown'}
Amount: ${deletedExpense.currency || 'PKR'} ${(deletedExpense.amount || 0).toLocaleString()}
Description: ${deletedExpense.description || 'No description'}
🆔 Database ID: ${deletedExpense.id}
${reason ? `Reason: ${reason}` : ''}

⚠️ This action cannot be undone.`;

          return res.json({
            message: successMessage,
            expenseData: deletedExpense,
            deleted: true,
            deletionType: 'expense'
          });

        } catch (error) {
          console.error('Error deleting expense:', error);
          return res.json({
            message: language === 'ur' ?
              'اخراجات حذف کرنے میں خرابی۔ براہ کرم دوبارہ کوشش کریں۔' :
              'Error deleting expense. Please try again.',
            error: true
          });
        }
      } else {
        // Invalid selection - show expense list again
        const errorMessage = language === 'ur' ?
          `❌ غلط انتخاب - براہ کرم کوئی اخراجات منتخب کریں۔` :
          `❌ Invalid Selection - Please select an expense.`;

        // Collect all expense images to show in the error message (card format)
        const expenseImages = availableExpenses.map(expense => {
          const amount = expense.amount || 0;
          const currency = expense.currency || 'PKR';
          const category = expense.category || 'Unknown';
          const description = expense.description || 'No description';
          const date = expense.date ? new Date(expense.date).toLocaleDateString() : 'Unknown date';
          const paymentMethod = expense.paymentMethod || 'Cash';

          return {
            imageUri: expense.receiptImage || null,
            name: category,
            amount: `${currency} ${amount.toLocaleString()}`,
            date: date,
            paymentMethod: paymentMethod,
            description: description.length > 50 ? description.substring(0, 50) + '...' : description,
            id: expense.id,
            category: category,
            fullAmount: amount,
            currency: currency
          };
        });

        return res.json({
          message: errorMessage,
          expenseImages: expenseImages,
          context: req.body.context
        });
      }
    }

    // Handle farm-related text prompts (without images)
    if (prompt && !imageUri && (
      prompt.toLowerCase().includes('farm') ||
      prompt.toLowerCase().includes('add this farm') ||
      prompt.toLowerCase().includes('create farm') ||
      prompt.toLowerCase().includes('farm name') ||
      prompt.toLowerCase().includes('farm type') ||
      prompt.toLowerCase().includes('acre') ||
      prompt.toLowerCase().includes('hectare') ||
      prompt.toLowerCase().includes('address') ||
      prompt.toLowerCase().includes('location')
    )) {

      try {
        // Extract farm information using AI
        const extractedInfo = await analyzePromptWithAI(prompt, 'farm');
        // Check if this is a farm creation request
        if (extractedInfo && (extractedInfo.action === 'add' || extractedInfo.action === 'save' ||
          extractedInfo.farmName || extractedInfo.farmLocation || extractedInfo.farmSize)) {

          // Create farm data from extracted information
          const farmName = extractedInfo.farmName || `Farm${Date.now().toString().slice(-4)}`;

          const completeFarmData = {
            name: farmName,
            farmType: extractedInfo.farmType || 'livestock',
            estimatedSize: extractedInfo.farmSize ? parseInt(extractedInfo.farmSize) : 50,
            sizeUnit: extractedInfo.sizeUnit || 'acre',
            structures: ['house', 'shed', 'fence'], // Default structures
            landType: 'mixed',
            vegetation: 'grass',
            description: `Farm created from text: ${prompt}`,
            imageUri: null, // No image for text-only farms
            analysisConfidence: extractedInfo.confidence || 90,
            readyToSave: true,
            extractedInfo: extractedInfo,
            dashboardLocation: extractedInfo.farmLocation || ""
          };

          previousFarmData = completeFarmData;

          // Build display message with only meaningful data (hide defaults)
          let displayParts = [];

          displayParts.push(` Name : ${farmName}`);

          if (extractedInfo.farmType) {
            displayParts.push(` Type : ${extractedInfo.farmType}`);
          }

          if (extractedInfo.farmSize) {
            displayParts.push(` Size : ${extractedInfo.farmSize} ${extractedInfo.sizeUnit || 'acre'}`);
          }

          if (extractedInfo.farmLocation) {
            displayParts.push(` Location : ${extractedInfo.farmLocation}`);
          }

          displayParts.push(` Confidence : ${extractedInfo.confidence || 90}%`);

          const userMessage = language === 'ur' ?
            `🏡  فارم کی تفصیلات :

${displayParts.join('\n')}

💡  اگلے قدم :
• نام تبدیل کرنے کے لیے: "نام تبدیل کر کے [نیا نام] رکھیں"
• پتہ تبدیل کرنے کے لیے: "پتہ تبدیل کرو [نیا پتہ]"
• قسم تبدیل کرنے کے لیے: "قسم تبدیل کرو [نئی قسم]"
• سائز تبدیل کرنے کے لیے: "سائز تبدیل کرو [نیا سائز]"
• محفوظ کرنے کے لیے: "محفوظ کریں"` :

            `🏡  Farm Analysis Results :

${displayParts.join('\n')}

💡  Next Steps :
• To change name: "change name to [new name]"
• To change address: "change address to [new address]"
• To change type: "change type to [new type]"
• To change size: "change size to [new size]"
• To save: "save"`;

          return res.json({
            message: userMessage,
            farmData: completeFarmData,
            extractedInfo: extractedInfo,
            imageType: 'farm',
            readyToSave: true
          });
        }
      } catch (error) {
        console.error('Error processing farm text prompt:', error);
      }
    }

    // Handle milking-related prompts
    if (prompt && (
      prompt.toLowerCase().includes('milk') ||
      prompt.toLowerCase().includes('milking') ||
      prompt.toLowerCase().includes('morning') ||
      prompt.toLowerCase().includes('evening') ||
      prompt.toLowerCase().includes('afternoon') ||
      prompt.toLowerCase().includes('liter') ||
      prompt.toLowerCase().includes('litre')
    )) {
      console.log('Processing milking-related prompt...');

      try {
        // Extract milking information using AI
        const milkingInfo = await analyzePromptWithAI(prompt, 'milking');
        console.log('AI extracted milking info:', milkingInfo);

        // Get selected farm from request body
        const selectedFarmFromDashboard = requestBody.farms;
        let selectedFarm = null;

        if (selectedFarmFromDashboard && selectedFarmFromDashboard.length > 0) {
          selectedFarm = selectedFarmFromDashboard[0];
          console.log('Using farm from dashboard for milking:', selectedFarm.name);
        } else {
          // Fallback: fetch user's farms if no farm data in request
          try {
            const farmsSnapshot = await firestore.collection('farms')
              .where('createdBy', '==', userId)
              .limit(1)
              .get();

            if (!farmsSnapshot.empty) {
              const farmDoc = farmsSnapshot.docs[0];
              selectedFarm = {
                id: farmDoc.id,
                ...farmDoc.data()
              };
              console.log('Using fallback farm for milking:', selectedFarm.name);
            }
          } catch (farmError) {
            console.error('Error fetching farms for milking:', farmError);
          }
        }

        if (!selectedFarm) {
          return res.json({
            message: language === 'ur' ?
              '❌  خرابی : پہلے فارم بنائیں۔' :
              '❌  Error : Please create a farm first.',
            error: true
          });
        }

        // Get all animals from the farm first for debugging
        const farmRef = firestore.collection('farms').doc(selectedFarm.id);
        const allAnimalsSnapshot = await farmRef.collection('animals').get();
        const allAnimals = allAnimalsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));

        console.log('All animals in farm:', allAnimals.length);
        console.log('Animal details:', allAnimals.map(a => ({ name: a.name, species: a.species, gender: a.gender })));

        // Get female animals (cows, goats) from the farm for milking
        const availableAnimals = allAnimals.filter(animal =>
          animal.gender === 'female' &&
          (animal.species?.toLowerCase() === 'cow' || animal.species?.toLowerCase() === 'goat')
        );

        console.log('Available female animals for milking:', availableAnimals.length);

        // Check if animal name was mentioned in the prompt
        let selectedAnimal = null;
        if (milkingInfo && milkingInfo.animalName) {
          selectedAnimal = availableAnimals.find(animal =>
            animal.name?.toLowerCase().includes(milkingInfo.animalName.toLowerCase())
          );
        }

        if (!selectedAnimal && availableAnimals.length === 0) {
          // Show information about available animals with picture indicators
          const animalInfo = allAnimals.length > 0 ?
            allAnimals.map(animal => {
              const basicInfo = `• ${animal.name || 'Unnamed'} (${animal.species || 'Unknown'}, ${animal.gender || 'Unknown gender'})`;
              if (animal.imageUri) {
                return `${basicInfo} 📸`;
              }
              return basicInfo;
            }).join('\n') :
            'No animals found in this farm.';

          const debugMessage = language === 'ur' ?
            `❌  کوئی دودھ دینے والا جانور دستیاب نہیں 

🔍  تجزیہ شدہ ڈیٹا :
⏰  وقت : ${(milkingInfo && milkingInfo.session) || 'نامعلوم'}
🥛  مقدار : ${(milkingInfo && milkingInfo.quantity) || 'نامعلوم'} لیٹر
⭐  کوالٹی : ${(milkingInfo && milkingInfo.quality) || 'نامعلوم'}
${(milkingInfo && milkingInfo.fat) ? `🧈  چربی : ${milkingInfo.fat}%\n` : ''}${(milkingInfo && milkingInfo.protein) ? `🥩  پروٹین : ${milkingInfo.protein}%\n` : ''}
📋  فارم میں موجود جانور :
${animalInfo}

⚠️  نوٹ : دودھ کے لیے صرف مادہ گائے اور بکری کا انتخاب کیا جا سکتا ہے۔` :
            `❌  No Milking Animals Available 

🔍  Extracted Data :
⏰  Session : ${(milkingInfo && milkingInfo.session) || 'Unknown'}
🥛  Quantity : ${(milkingInfo && milkingInfo.quantity) || 'Unknown'} liters
⭐  Quality : ${(milkingInfo && milkingInfo.quality) || 'Unknown'}
${(milkingInfo && milkingInfo.fat) ? `🧈  Fat : ${milkingInfo.fat}%\n` : ''}${(milkingInfo && milkingInfo.protein) ? `🥩  Protein : ${milkingInfo.protein}%\n` : ''}
📋  Animals in Farm :
${animalInfo}

⚠️  Note : Only female cows and goats can be selected for milking.`;

          return res.json({
            message: debugMessage,
            extractedData: milkingInfo,
            availableAnimals: allAnimals,
            error: true
          });
        }

        if (!selectedAnimal && availableAnimals.length > 0) {
          const selectionMessage = language === 'ur' ?
            `🐄 جانور کا انتخاب کریں` :
            `🐄 Select Animal for Milking`;

          // Collect all animal images to show in the selection
          const animalImages = availableAnimals
            .map(animal => ({
              imageUri: animal.imageUri,
              name: animal.name,
              species: animal.species,
              id: animal.id
            }));

          return res.json({
            message: selectionMessage,
            animalImages: animalImages, // Send all animal images
            context: {
              milkingData: milkingInfo,
              selectedFarm,
              availableAnimals,
              needsAnimalSelection: true
            }
          });
        }

        // If we have all required data, prepare for saving
        if (selectedAnimal && milkingInfo && milkingInfo.session && milkingInfo.quantity) {
          const milkingData = {
            farmId: selectedFarm.id,
            farmName: selectedFarm.name,
            animalId: selectedAnimal.id,
            animalName: selectedAnimal.name,
            animalSpecies: selectedAnimal.species,
            date: Date.now(),
            session: milkingInfo.session,
            quantity: parseFloat(milkingInfo.quantity),
            quality: milkingInfo.quality || 'good',
            fat: milkingInfo.fat ? parseFloat(milkingInfo.fat) : undefined,
            protein: milkingInfo.protein ? parseFloat(milkingInfo.protein) : undefined,
            temperature: milkingInfo.temperature ? parseFloat(milkingInfo.temperature) : undefined,
            notes: milkingInfo.notes || undefined,
            createdBy: userId,
            tenantId: userId
          };

          const confirmationMessage = language === 'ur' ?
            `📋  دودھ کی تفصیلات 

🐄  جانور : ${selectedAnimal.name} (${selectedAnimal.species})
⏰  وقت : ${milkingInfo.session}
🥛  مقدار : ${milkingInfo.quantity} لیٹر
⭐  کوالٹی : ${milkingInfo.quality || 'اچھی'}
${milkingInfo.fat ? `🧈  چربی : ${milkingInfo.fat}%\n` : ''}${milkingInfo.protein ? `🥩  پروٹین : ${milkingInfo.protein}%\n` : ''}${milkingInfo.temperature ? `🌡️  درجہ حرارت : ${milkingInfo.temperature}°C\n` : ''}${milkingInfo.notes ? `📝  نوٹس : ${milkingInfo.notes}\n` : ''}
کیا آپ یہ ڈیٹا محفوظ کرنا چاہتے ہیں؟  "Yes"  ٹائپ کریں۔` :
            `📋  Milking Record Details 

🐄  Animal : ${selectedAnimal.name} (${selectedAnimal.species})
⏰  Session : ${milkingInfo.session}
🥛  Quantity : ${milkingInfo.quantity} liters
⭐  Quality : ${milkingInfo.quality || 'good'}
${milkingInfo.fat ? `🧈  Fat : ${milkingInfo.fat}%\n` : ''}${milkingInfo.protein ? `🥩  Protein : ${milkingInfo.protein}%\n` : ''}${milkingInfo.temperature ? `🌡️  Temperature : ${milkingInfo.temperature}°C\n` : ''}${milkingInfo.notes ? `📝  Notes : ${milkingInfo.notes}\n` : ''}
Do you want to save this data? Type  "Yes"  to confirm.`;

          return res.json({
            message: confirmationMessage,
            image: selectedAnimal.imageUri || undefined, // Show the selected animal's picture
            animalInfo: {
              name: selectedAnimal.name,
              species: selectedAnimal.species,
              id: selectedAnimal.id
            },
            context: {
              milkingData,
              selectedFarm,
              selectedAnimal,
              readyToSave: true,
              needsAnimalSelection: false
            }
          });
        }
        debugger
        // If missing required data, ask for more information
        const missingFields = [];
        if (!milkingInfo || !milkingInfo.session) missingFields.push(language === 'ur' ? 'وقت (صبح/دوپہر/شام)' : 'session (morning/afternoon/evening)');
        if (!milkingInfo || !milkingInfo.quantity) missingFields.push(language === 'ur' ? 'مقدار (لیٹر میں)' : 'quantity (in liters)');

        const missingMessage = language === 'ur' ?
          `❌  ناکافی معلومات 

براہ کرم یہ معلومات فراہم کریں:
${missingFields.map(field => `• ${field}`).join('\n')}

مثال: "صبح کے وقت گائے سے 15 لیٹر دودھ نکالا، کوالٹی اچھی ہے"` :
          `❌  Missing Information 

Please provide the following details:
${missingFields.map(field => `• ${field}`).join('\n')}

Example: "Milked 15 liters from cow in the morning session, quality is good"`;

        return res.json({
          message: missingMessage,
          context: {
            milkingData: milkingInfo,
            selectedFarm,
            availableAnimals,
            needsMoreInfo: true
          }
        });

      } catch (error) {
        console.error('Error processing milking prompt:', error);
        return res.json({
          message: language === 'ur' ?
            'دودھ کی معلومات پروسیس کرنے میں خرابی۔ براہ کرم دوبارہ کوشش کریں۔' :
            'Error processing milking information. Please try again.',
          error: true
        });
      }
    }

    // Handle farm deletion prompts
    if (requestType === 'delete_farm') {
      console.log('Processing farm deletion prompt...');

      try {
        // Extract farm deletion information using AI
        const deleteInfo = await analyzePromptWithAI(prompt, 'farm_delete');
        console.log('AI extracted delete info:', deleteInfo);

        // Get available farms from request body
        const availableFarms = requestBody.farms || [];

        if (deleteInfo && deleteInfo.isDeletion && deleteInfo.action &&
          ['delete', 'remove', 'demolish', 'destroy', 'eliminate'].includes(deleteInfo.action)) {
          // If farm name is specified, try to find and delete it directly
          if (deleteInfo.farmName) {
            console.log('Looking for farm with name:', deleteInfo.farmName);

            // Find farm by name (case-insensitive partial match)
            const targetFarm = availableFarms.find(farm =>
              farm.name.toLowerCase().includes(deleteInfo.farmName.toLowerCase()) ||
              deleteInfo.farmName.toLowerCase().includes(farm.name.toLowerCase())
            );

            if (targetFarm) {
              console.log('Found target farm for deletion:', targetFarm.name);

              try {
                // Delete the farm
                const deletedFarm = await deleteFarmFromDatabase(targetFarm.id, userId);

                const successMessage = language === 'ur' ?
                  `✅ فارم کامیابی سے حذف ہو گیا:

نام: ${deletedFarm.name}
🆔 ڈیٹابیس ID: ${deletedFarm.id}

✅ تمام متعلقہ ڈیٹا (جانور، اخراجات، دودھ کے ریکارڈ، وغیرہ) بھی حذف ہو گیا۔` :
                  `✅ Farm Successfully Deleted:

Name: ${deletedFarm.name}
🆔 Database ID: ${deletedFarm.id}

✅ All related data (animals, expenses, milking records, etc.) has also been deleted.`;

                return res.json({
                  message: successMessage,
                  farmData: deletedFarm,
                  deleted: true,
                  databaseId: deletedFarm.id
                });
              } catch (deleteError) {
                console.error('Error deleting farm:', deleteError);

                const errorMessage = language === 'ur' ?
                  `❌ فارم حذف کرنے میں خرابی: ${deleteError.message}` :
                  `❌ Error deleting farm: ${deleteError.message}`;

                return res.json({
                  message: errorMessage,
                  error: true
                });
              }
            } else {
              // Farm not found with specified name
              const notFoundMessage = language === 'ur' ?
                `❌ "${deleteInfo.farmName}" نام کا فارم نہیں ملا۔

دستیاب فارمز:
${availableFarms.map(farm => `• ${farm.name}`).join('\n')}

براہ کرم صحیح نام استعمال کریں یا فہرست سے انتخاب کریں۔` :
                `❌ Farm "${deleteInfo.farmName}" not found.

Available farms:
${availableFarms.map(farm => `• ${farm.name}`).join('\n')}

Please use the correct name or select from the list.`;

              return res.json({
                message: notFoundMessage,
                error: true,
                availableFarms: availableFarms.map(farm => ({
                  id: farm.id,
                  name: farm.name,
                  location: typeof farm.location === 'string' ? farm.location : farm.location?.address || 'No location'
                }))
              });
            }
          } else {
            // No farm name specified, fetch ALL user's farms and show visual selection list
            try {
              console.log('Fetching all farms for user:', userId);

              // Get user data first to check role and permissions
              const userRef = firestore.collection('users').doc(userId);
              const userDoc = await userRef.get();

              let userData = null;
              let allUserFarms = [];

              if (userDoc.exists) {
                userData = userDoc.data();
                const assignedFarmIds = userData.assignedFarmIds || [];

                if (assignedFarmIds.length > 0) {
                  // New logic: Fetch all farms from the assigned list
                  const farmPromises = assignedFarmIds.map(async (farmId) => {
                    const farmDoc = await firestore.collection('farms').doc(farmId).get();
                    if (farmDoc.exists) {
                      const farmData = farmDoc.data();
                      return {
                        id: farmDoc.id,
                        name: farmData.name || 'Unnamed Farm',
                        location: farmData.location,
                        photoURL: farmData.photoURL || null,
                        ...farmData
                      };
                    }
                    return null;
                  });
                  const farmResults = await Promise.all(farmPromises);
                  allUserFarms = farmResults.filter(farm => farm !== null);
                }
              }

              // Fallback for older owner accounts that might not have assignedFarmIds
              if (allUserFarms.length === 0 && userData?.role === 'owner') {
                console.log('No assigned farms found for owner, falling back to ownerId query.');
                const farmsSnapshot = await firestore.collection('farms')
                  .where('ownerId', '==', userId)
                  .get();

                farmsSnapshot.docs.forEach(doc => {
                  const farmData = doc.data();
                  allUserFarms.push({
                    id: doc.id,
                    name: farmData.name || 'Unnamed Farm',
                    location: farmData.location,
                    photoURL: farmData.photoURL || null,
                    ...farmData
                  });
                });
              }

              console.log('Found farms for deletion:', allUserFarms);

              if (allUserFarms.length === 0) {
                const noFarmsMessage = language === 'ur' ?
                  '❌ کوئی فارم دستیاب نہیں ہے۔' :
                  '❌ No farms available.';

                return res.json({
                  message: noFarmsMessage,
                  error: true
                });
              }

              const selectionMessage = language === 'ur' ?
                `🗑️ فارم حذف کرنا

📋 آپ کے پاس ${allUserFarms.length} فارم${allUserFarms.length > 1 ? 'ز' : ''} ہے${allUserFarms.length > 1 ? 'ں' : ''}۔ براہ کرم اس فارم کا انتخاب کریں جسے آپ حذف کرنا چاہتے ہیں:

⚠️ انتباہ: فارم حذف کرنے سے تمام متعلقہ ڈیٹا (جانور، اخراجات، دودھ کے ریکارڈ، وغیرہ) بھی حذف ہو جائے گا۔
🔒 صرف فارم کے مالک یا تفویض کردہ ایڈمن ہی فارم حذف کر سکتے ہیں۔` :
                `🗑️ Delete Farm

📋 You have ${allUserFarms.length} farm${allUserFarms.length > 1 ? 's' : ''} available. Please select the farm you want to delete:

⚠️ Warning: Deleting a farm will also delete all related data (animals, expenses, milking records, etc.).
🔒 Only farm owners or assigned admins can delete farms.`;

              // Collect all farm images for display (similar to animal selection)
              const farmImages = allUserFarms.map(farm => ({
                imageUri: farm.photoURL || null, // Ensure null if no image
                name: farm.name || 'Unnamed Farm',
                location: typeof farm.location === 'string' ? farm.location :
                  (farm.location?.address || 'No location specified'),
                id: farm.id,
                size: farm.size ? `${farm.size} ${farm.sizeUnit || 'acres'}` : 'Size not specified',
                type: farm.type || 'Farm'
              }));

              console.log('🖼️ Farm images for visual selection:', JSON.stringify(farmImages, null, 2));
              console.log('📊 Response flags:', {
                needsFarmSelection: true,
                selectionType: 'farm_deletion',
                visualInterface: true,
                actionType: 'destructive',
                totalFarms: allUserFarms.length
              });

              return res.json({
                message: selectionMessage,
                farmImages: farmImages, // Send all farm images like animal selection
                needsFarmSelection: true,
                selectionType: 'farm_deletion',
                totalFarms: allUserFarms.length,
                farmList: allUserFarms.map(farm => ({
                  id: farm.id,
                  label: farm.name || 'Unnamed Farm',
                  description: `${typeof farm.location === 'string' ? farm.location : farm.location?.address || 'No location'} • ${farm.size || 'Unknown'} ${farm.sizeUnit || 'acres'}`,
                  imageUri: farm.photoURL || null,
                  type: farm.type || 'Farm',
                  size: farm.size,
                  sizeUnit: farm.sizeUnit
                })),
                context: {
                  action: 'delete_farm',
                  needsFarmSelection: true,
                  availableFarms: allUserFarms,
                  userRole: userData?.role || 'unknown'
                },
                visualInterface: true, // Flag to indicate this supports visual selection
                actionType: 'destructive' // Indicates this is a destructive action
              });
            } catch (error) {
              console.error('Error fetching farms for deletion:', error);
              return res.json({
                message: language === 'ur' ?
                  'فارمز کی فہرست لانے میں خرابی۔ براہ کرم دوبارہ کوشش کریں۔' :
                  'Error fetching farms list. Please try again.',
                error: true
              });
            }
          }
        } else {
          // Not a valid deletion request - provide helpful guidance
          console.log('Invalid deletion request detected:', deleteInfo);

          const invalidMessage = language === 'ur' ?
            `❌ یہ فارم حذف کرنے کی درخواست نہیں لگ رہی۔

🗑️ فارم حذف کرنے کے لیے استعمال کریں:
• "Delete [Farm Name]"
• "Remove [Farm Name]"
• "Demolish [Farm Name]"
• "حذف کریں [Farm Name]"
• "فارم کو ہٹا دیں"

⚠️ نوٹ: صرف فارم کے مالک یا تفویض کردہ ایڈمن ہی فارم حذف کر سکتے ہیں۔` :
            `❌ This does not appear to be a farm deletion request.

🗑️ To delete a farm, use commands like:
• "Delete [Farm Name]"
• "Remove [Farm Name]"
• "Demolish [Farm Name]"
• "Destroy [Farm Name]"

⚠️ Note: Only farm owners or assigned admins can delete farms.`;

          return res.json({
            message: invalidMessage,
            error: true,
            helpType: 'deletion_syntax'
          });
        }
      } catch (error) {
        console.error('Error processing farm deletion:', error);
        return res.json({
          message: language === 'ur' ?
            'فارم حذف کرنے کی درخواست پروسیس کرنے میں خرابی۔ براہ کرم دوبارہ کوشش کریں۔' :
            'Error processing farm deletion request. Please try again.',
          error: true
        });
      }
    }

    // Handle task-related prompts
    if (requestType === 'add_task') {
      console.log('Processing task creation prompt...');

      try {
        // Extract task information using AI
        const taskInfo = await analyzePromptWithAI(prompt, 'add_task');
        console.log('AI extracted task info:', taskInfo);

        // Get context from request body
        const selectedFarmFromDashboard = requestBody.farms;
        let selectedFarm = null;

        console.log('Request body farms:', selectedFarmFromDashboard);
        console.log('User ID:', userId);

        if (selectedFarmFromDashboard && selectedFarmFromDashboard.length > 0) {
          selectedFarm = selectedFarmFromDashboard[0];
          console.log('Using farm from dashboard for task:', {
            id: selectedFarm.id,
            name: selectedFarm.name,
            ownerId: selectedFarm.ownerId
          });
        } else {
          // Fallback: fetch user's farms if no farm data in request
          try {
            const farmsSnapshot = await firestore.collection('farms')
              .where('ownerId', '==', userId)
              .get();

            const userFarms = farmsSnapshot.docs.map(doc => ({
              id: doc.id,
              ...doc.data()
            }));

            selectedFarm = userFarms.find(f => f.name !== 'All farms') || userFarms[0];
            console.log('Using fallback farm for task:', selectedFarm?.name);
          } catch (farmError) {
            console.error('Error fetching farms for task:', farmError);
          }
        }

        if (!selectedFarm) {
          return res.json({
            message: language === 'ur' ?
              'کوئی فارم دستیاب نہیں۔ پہلے فارم بنائیں۔' :
              'No farms available. Please create a farm first.',
            error: true
          });
        }

        // Validate title
        if (!taskInfo || !taskInfo.title) {
          return res.json({
            message: language === 'ur' ?
              'کام کا عنوان درکار ہے۔ براہ کرم کام کی تفصیل بتائیں۔' :
              'Task title is required. Please specify what task needs to be done.',
            error: true
          });
        }

        // Handle assignee validation
        if (!taskInfo.assigneeName) {
          // Fetch employees for the selected farm
          try {
            console.log('Fetching employees for farm:', selectedFarm.id);

            // Get employees from users collection who are assigned to this farm
            console.log('Querying users with assignedFarmIds containing:', selectedFarm.id);
            const usersSnapshot = await firestore.collection('users')
              .where('assignedFarmIds', 'array-contains', selectedFarm.id)
              .get();

            console.log('Users query returned:', usersSnapshot.docs.length, 'documents');

            const employees = [];
            usersSnapshot.docs.forEach(doc => {
              const userData = doc.data();
              console.log('Processing user:', {
                id: doc.id,
                name: userData.name,
                role: userData.role,
                assignedFarmIds: userData.assignedFarmIds
              });

              employees.push({
                id: doc.id,
                name: userData.name || 'Unnamed Employee',
                imageUri: userData.photo || userData.photoURL || null,
                role: userData.role || 'caretaker'
              });
            });

            // Also check if the farm owner should be included
            if (selectedFarm.ownerId && selectedFarm.ownerId === userId) {
              console.log('Adding farm owner as potential assignee');
              try {
                const ownerDoc = await firestore.collection('users').doc(userId).get();
                if (ownerDoc.exists) {
                  const ownerData = ownerDoc.data();
                  // Check if owner is not already in the list
                  if (!employees.find(emp => emp.id === userId)) {
                    employees.push({
                      id: userId,
                      name: ownerData.name || 'Farm Owner',
                      imageUri: ownerData.photo || ownerData.photoURL || null,
                      role: ownerData.role || 'owner'
                    });
                    console.log('Added farm owner to employee list');
                  }
                }
              } catch (ownerError) {
                console.error('Error fetching owner data:', ownerError);
              }
            }

            // If no employees found and this looks like a test environment, add mock employees
            if (employees.length === 0 && (selectedFarm.id.includes('test') || selectedFarm.name.toLowerCase().includes('test') || process.env.NODE_ENV === 'development')) {
              console.log('🧪 Adding mock employees for testing...');
              employees.push(
                {
                  id: 'mock-emp-1',
                  name: 'John Doe',
                  imageUri: null,
                  role: 'caretaker'
                },
                {
                  id: 'mock-emp-2',
                  name: 'Sarah Smith',
                  imageUri: null,
                  role: 'admin'
                },
                {
                  id: userId, // Add current user as option
                  name: 'Farm Owner (You)',
                  imageUri: null,
                  role: 'owner'
                }
              );
              console.log('Added mock employees for testing');
            }

            console.log('Final employee list:', employees.length, 'employees');
            employees.forEach((emp, index) => {
              console.log(`  ${index + 1}. ${emp.name} (${emp.role}) - ID: ${emp.id}`);
            });

            if (employees.length === 0) {
              // Provide detailed debugging information
              console.log('❌ No employees found for farm:', selectedFarm.id);
              console.log('Farm details:', {
                id: selectedFarm.id,
                name: selectedFarm.name,
                ownerId: selectedFarm.ownerId
              });
              console.log('Current user ID:', userId);

              // Try to fetch all users to see what's available
              try {
                const allUsersSnapshot = await firestore.collection('users').get();
                console.log('Total users in system:', allUsersSnapshot.docs.length);

                const usersWithFarms = [];
                allUsersSnapshot.docs.forEach(doc => {
                  const userData = doc.data();
                  if (userData.assignedFarmIds && userData.assignedFarmIds.length > 0) {
                    usersWithFarms.push({
                      id: doc.id,
                      name: userData.name,
                      assignedFarmIds: userData.assignedFarmIds,
                      role: userData.role
                    });
                  }
                });

                console.log('Users with assigned farms:', usersWithFarms.length);
                usersWithFarms.forEach(user => {
                  console.log(`  - ${user.name} (${user.role}): farms ${user.assignedFarmIds.join(', ')}`);
                });

              } catch (debugError) {
                console.error('Error in debug query:', debugError);
              }

              return res.json({
                message: language === 'ur' ?
                  `اس فارم میں کوئی ملازم نہیں ملا۔

🏡  فارم : ${selectedFarm.name}
🆔  فارم ID : ${selectedFarm.id}

💡  ممکنہ حل :
• فارم میں ملازم شامل کریں
• یقینی بنائیں کہ ملازمین اس فارم کے لیے تفویض ہیں
• فارم کی سیٹنگز چیک کریں` :
                  `No employees found for this farm.

🏡  Farm : ${selectedFarm.name}
🆔  Farm ID : ${selectedFarm.id}

💡  Possible solutions :
• Add employees to this farm
• Ensure employees are assigned to this farm
• Check farm settings and permissions

🔧  Debug info : Farm owner: ${selectedFarm.ownerId}, Current user: ${userId}`,
                error: true,
                debugInfo: {
                  farmId: selectedFarm.id,
                  farmName: selectedFarm.name,
                  farmOwnerId: selectedFarm.ownerId,
                  currentUserId: userId,
                  employeeCount: 0
                }
              });
            }

            // Return employee selection prompt with visual interface
            return res.json({
              message: language === 'ur' ?
                `✅ کام کی تفصیل: ${taskInfo.title}
🏡 فارم: ${selectedFarm.name}
` :
                `✅ Task: ${taskInfo.title}
🏡 Farm: ${selectedFarm.name}
`,
              needsEmployeeSelection: true,
              employeeList: employees.map(emp => ({
                id: emp.id,
                label: emp.name,
                imageUri: emp.imageUri,
                role: emp.role,
                // Additional display info for better selection
                description: `${emp.role.charAt(0).toUpperCase() + emp.role.slice(1)} • ${selectedFarm.name}`
              })),
              selectionType: 'employee_dropdown', // Indicates this should show a dropdown/list interface
              context: {
                taskData: {
                  title: taskInfo.title,
                  description: taskInfo.description || '',
                  farmId: selectedFarm.id,
                  farmName: selectedFarm.name,
                  dueDate: taskInfo.dueDate,
                  priority: taskInfo.priority || 'medium',
                  recurrence: taskInfo.recurrence || 'none',
                  notes: taskInfo.notes || ''
                },
                needsEmployeeSelection: true
              }
            });
          } catch (employeeError) {
            console.error('Error fetching employees:', employeeError);
            return res.json({
              message: language === 'ur' ?
                'ملازمین کی فہرست لانے میں خرابی۔' :
                'Error fetching employee list.',
              error: true
            });
          }
        } else {
          // Assignee name provided, find the employee
          try {
            const usersSnapshot = await firestore.collection('users')
              .where('assignedFarmIds', 'array-contains', selectedFarm.id)
              .get();

            let foundEmployee = null;
            usersSnapshot.docs.forEach(doc => {
              const userData = doc.data();
              if (userData.name && userData.name.toLowerCase().includes(taskInfo.assigneeName.toLowerCase())) {
                foundEmployee = {
                  id: doc.id,
                  name: userData.name,
                  role: userData.role || 'caretaker'
                };
              }
            });

            if (!foundEmployee) {
              return res.json({
                message: language === 'ur' ?
                  `ملازم '${taskInfo.assigneeName}' نہیں ملا۔ براہ کرم دستیاب ملازمین میں سے انتخاب کریں۔` :
                  `Employee '${taskInfo.assigneeName}' not found. Please choose from available employees.`,
                error: true
              });
            }

            // Create and save the task
            const taskData = await createAndSaveTask(taskInfo, selectedFarm, foundEmployee, userId, language);
            return res.json(taskData);
          } catch (employeeSearchError) {
            console.error('Error searching for employee:', employeeSearchError);
            return res.json({
              message: language === 'ur' ?
                'ملازم تلاش کرنے میں خرابی۔' :
                'Error searching for employee.',
              error: true
            });
          }
        }
      } catch (error) {
        console.error('Error processing task:', error);
        return res.json({
          message: language === 'ur' ?
            'کام بنانے میں خرابی۔ براہ کرم دوبارہ کوشش کریں۔' :
            'Error creating task. Please try again.',
          error: true
        });
      }
    }

    // Handle pregnancy-related prompts
    if (requestType === 'add_pregnancy' || (prompt && (
      prompt.toLowerCase().includes('pregnant') ||
      prompt.toLowerCase().includes('pregnancy') ||
      prompt.toLowerCase().includes('mating') ||
      prompt.toLowerCase().includes('ai cross') ||
      prompt.toLowerCase().includes('artificial insemination') ||
      prompt.toLowerCase().includes('conception')
    ))) {

      try {
        // Extract pregnancy information using AI
        const pregnancyInfo = await analyzePromptWithAI(prompt, 'pregnancy');
        console.log('AI extracted pregnancy info:', pregnancyInfo);

        // Get selected farm from request body
        const selectedFarmFromDashboard = requestBody.farms;
        let selectedFarm = null;

        if (selectedFarmFromDashboard && selectedFarmFromDashboard.length > 0) {
          selectedFarm = selectedFarmFromDashboard[0];
          console.log('Using farm from dashboard for pregnancy:', selectedFarm.name);
        } else {
          // Fallback: fetch user's farms if no farm data in request
          try {
            const farmsSnapshot = await firestore.collection('farms')
              .where('ownerId', '==', userId)
              .limit(1)
              .get();

            if (!farmsSnapshot.empty) {
              const farmDoc = farmsSnapshot.docs[0];
              selectedFarm = {
                id: farmDoc.id,
                ...farmDoc.data()
              };
              console.log('Using fallback farm for pregnancy:', selectedFarm.name);
            }
          } catch (farmError) {
            console.error('Error fetching farms for pregnancy:', farmError);
          }
        }

        if (!selectedFarm) {
          return res.json({
            message: language === 'ur' ?
              '❌ خرابی: پہلے فارم بنائیں۔' :
              '❌ Error: Please create a farm first.',
            error: true
          });
        }

        // Get all animals from the farm
        const farmRef = firestore.collection('farms').doc(selectedFarm.id);
        const allAnimalsSnapshot = await farmRef.collection('animals').get();
        const allAnimals = allAnimalsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));

        console.log('All animals in farm for pregnancy:', allAnimals.length);

        // Prepare pregnancy data
        const pregnancyData = {
          farmId: selectedFarm.id,
          animalName: pregnancyInfo?.animalName || null,
          animalId: pregnancyInfo?.animalId || null,
          sireName: pregnancyInfo?.sireName || null,
          sireId: pregnancyInfo?.sireId || null,
          conceptionDate: pregnancyInfo?.conceptionDate || null,
          status: pregnancyInfo?.status || 'suspected',
          isAICross: pregnancyInfo?.isAICross || false,
          useAIPlans: requestBody.useAIPlans || pregnancyInfo?.useAIPlans || false,
          createdBy: userId
        };

        // Validate pregnancy data
        const validation = validatePregnancyData(pregnancyData, allAnimals);

        if (!validation.isValid) {
          const missingInfo = findMissingPregnancyInfo(pregnancyData);
          const missingMessage = generateMissingInfoMessage(missingInfo, language);

          return res.json({
            message: missingMessage,
            context: {
              pregnancyData,
              selectedFarm,
              availableAnimals: allAnimals,
              needsMoreInfo: true
            }
          });
        }

        // If validation passed, prepare final pregnancy data
        const finalPregnancyData = validation.validatedData;

        // Get animal details for the response
        const selectedAnimal = allAnimals.find(a => a.id === finalPregnancyData.animalId);
        if (selectedAnimal) {
          finalPregnancyData.animalName = selectedAnimal.name;
          finalPregnancyData.species = selectedAnimal.species;
        }

        // Generate AI plans if requested
        if (finalPregnancyData.useAIPlans && selectedAnimal) {
          try {
            console.log(`Generating AI plans for pregnant animal in ${language}...`);
            const [dietPlan, healthPlan] = await Promise.all([
              generateAIDietPlan(selectedAnimal, language),
              generateAIHealthPlan(selectedAnimal, language)
            ]);

            finalPregnancyData.aiGeneratedDietPlan = dietPlan;
            finalPregnancyData.aiGeneratedHealthPlan = healthPlan;
            console.log('AI plans generated successfully');
          } catch (planError) {
            console.error('Error generating AI plans:', planError);
            // Continue without AI plans if generation fails
            finalPregnancyData.aiGeneratedDietPlan = null;
            finalPregnancyData.aiGeneratedHealthPlan = null;
          }
        } else {
          finalPregnancyData.aiGeneratedDietPlan = null;
          finalPregnancyData.aiGeneratedHealthPlan = null;
        }

        // Add timestamps
        finalPregnancyData.createdAt = new Date().toISOString();

        // Save pregnancy to database
        const savedPregnancy = await savePregnancyToDatabase(finalPregnancyData);

        const successMessage = language === 'ur' ?
          `✅ حمل کا ریکارڈ محفوظ ہو گیا!

🐄 جانور: ${finalPregnancyData.animalName} (${finalPregnancyData.species})
${finalPregnancyData.isAICross ?
            '🧬 قسم: AI Cross (مصنوعی بارآوری)' :
            `🐂 نر جانور: ${finalPregnancyData.sireName}`}
📅 حمل کی تاریخ: ${new Date(finalPregnancyData.conceptionDate).toLocaleDateString()}
📊 حالت: ${finalPregnancyData.status}
${finalPregnancyData.useAIPlans ? '🤖 AI منصوبے: شامل کیے گئے' : ''}
📋 ID: ${savedPregnancy.id}

💾 ڈیٹا کامیابی سے محفوظ ہو گیا ہے۔` :
          `✅ Pregnancy Record Saved Successfully!

🐄 Animal: ${finalPregnancyData.animalName} (${finalPregnancyData.species})
${finalPregnancyData.isAICross ?
            '🧬 Type: AI Cross (Artificial Insemination)' :
            `🐂 Sire: ${finalPregnancyData.sireName}`}
📅 Conception Date: ${new Date(finalPregnancyData.conceptionDate).toLocaleDateString()}
📊 Status: ${finalPregnancyData.status}
${finalPregnancyData.useAIPlans ? '🤖 AI Plans: Generated and included' : ''}
📋 ID: ${savedPregnancy.id}

💾 Data has been successfully saved to your records.`;

        return res.json({
          message: successMessage,
          pregnancySaved: true,
          pregnancyId: savedPregnancy.id,
          pregnancyData: savedPregnancy
        });

      } catch (error) {
        console.error('Error processing pregnancy prompt:', error);
        return res.json({
          message: language === 'ur' ?
            'حمل کی معلومات پروسیس کرنے میں خرابی۔ براہ کرم دوبارہ کوشش کریں۔' :
            'Error processing pregnancy information. Please try again.',
          error: true
        });
      }
    }

    // Handle task deletion prompts
    if (requestType === 'delete_task') {
      console.log('Processing task deletion prompt...');

      try {
        // Extract task deletion information using AI
        const deletionInfo = await analyzeTaskDeletionRequest(prompt);
        console.log('AI extracted deletion info:', deletionInfo);

        // Get context from request body
        const selectedFarmFromDashboard = requestBody.farms;
        let selectedFarm = null;

        console.log('Request body farms:', selectedFarmFromDashboard);
        console.log('User ID:', userId);

        if (selectedFarmFromDashboard && selectedFarmFromDashboard.length > 0) {
          selectedFarm = selectedFarmFromDashboard[0];
          console.log('Using farm from dashboard for task deletion:', {
            id: selectedFarm.id,
            name: selectedFarm.name
          });
        } else {
          console.log('No farm provided in request body');
          return res.json({
            message: language === 'ur' ?
              'فارم کی معلومات نہیں ملیں۔ براہ کرم فارم منتخب کریں۔' :
              'Farm information not found. Please select a farm.',
            error: true
          });
        }

        if (deletionInfo && deletionInfo.action === 'delete' && deletionInfo.confidence > 70) {
          
          if (userRole === 'caretaker') {
             return res.json({
              message: "Permission denied: Only farm owner or admin can delete tasks.",
              deleted: false,
               error: true
            });
            
          }
          
          const result = await deleteTask(deletionInfo, selectedFarm, userId, language);
          // Process task deletion

          if (result.deleted) {
            // Task was successfully deleted
            return res.json({
              message: result.message,
              taskData: result.taskData,
              deleted: true,
              databaseId: result.taskData.id
            });
          } else if (result.needsTaskSelection) {
            // Need to show task selection list
            return res.json({
              message: result.message,
              needsTaskSelection: true,
              taskList: result.taskList,
              selectionType: result.selectionType,
              context: result.context,
              error: false
            });
          } else if (result.error) {
            // Error occurred
            return res.json({
              message: result.message,
              error: true
            });
          }
        } else {
          // Invalid deletion request
          const invalidMessage = language === 'ur' ?
            `❌ یہ کام حذف کرنے کی درخواست نہیں لگ رہی۔

🗑️ کام حذف کرنے کے لیے استعمال کریں:
• "Delete the task"
• "Remove task [task name]"
• "Cancel the [task name] task"
• "کام حذف کریں"
• "کام ہٹا دیں"

💡 مثال: "Delete the cleaning task" یا "Remove task assigned to John"` :
            `❌ This does not appear to be a task deletion request.

🗑️ To delete a task, use commands like:
• "Delete the task"
• "Remove task [task name]"
• "Cancel the [task name] task"
• "Delete task assigned to [person]"

💡 Example: "Delete the cleaning task" or "Remove task assigned to John"`;

          return res.json({
            message: invalidMessage,
            error: true,
            helpType: 'task_deletion_syntax'
          });
        }

      } catch (error) {
        console.error('Error processing task deletion:', error);
        return res.json({
          message: language === 'ur' ?
            'کام حذف کرنے میں خرابی۔ براہ کرم دوبارہ کوشش کریں۔' :
            'Error processing task deletion. Please try again.',
          error: true
        });
      }
    }

    // Handle animal deletion prompts
    if (requestType === 'delete_animal') {
      console.log('Processing animal deletion prompt...');

      try {
        // Extract animal deletion information using AI
        const deletionInfo = await analyzePromptWithAI(prompt, 'animal_delete');
        console.log('AI extracted animal deletion info:', deletionInfo);

        // Get context from request body
        const selectedFarmFromDashboard = requestBody.farms;
        let selectedFarm = null;

        if (selectedFarmFromDashboard && selectedFarmFromDashboard.length > 0) {
          selectedFarm = selectedFarmFromDashboard[0];
          console.log('Using farm from dashboard for animal deletion:', selectedFarm.name);
        } else {
          // Fallback: fetch user's farms if no farm data in request
          try {
            const farmsSnapshot = await firestore.collection('farms')
              .where('ownerId', '==', userId)
              .get();

            const userFarms = farmsSnapshot.docs.map(doc => ({
              id: doc.id,
              ...doc.data()
            }));

            selectedFarm = userFarms.find(f => f.name !== 'All farms') || userFarms[0];
            console.log('Using fallback farm for animal deletion:', selectedFarm?.name);
          } catch (farmError) {
            console.error('Error fetching farms for animal deletion:', farmError);
          }
        }

        if (!selectedFarm) {
          return res.json({
            message: language === 'ur' ?
              'کوئی فارم دستیاب نہیں۔ پہلے فارم بنائیں۔' :
              'No farms available. Please create a farm first.',
            error: true
          });
        }

        if (deletionInfo && deletionInfo.action === 'delete_animal' && deletionInfo.confidence > 70) {
          // Get all animals from the farm
          const farmRef = firestore.collection('farms').doc(selectedFarm.id);
          const animalsSnapshot = await farmRef.collection('animals').get();
          const allAnimals = animalsSnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          }));

          console.log('Available animals for deletion:', allAnimals.length);

          // Check if specific animal was mentioned
          let selectedAnimal = null;
          if (deletionInfo.animalName) {
            // Try to find animal by name or tag ID
            selectedAnimal = allAnimals.find(animal => {
              const nameMatch = animal.name?.toLowerCase().includes(deletionInfo.animalName.toLowerCase());
              const tagMatch = deletionInfo.animalTagId &&
                (animal.tagId === deletionInfo.animalTagId ||
                  animal.name?.toLowerCase().includes(deletionInfo.animalTagId.toLowerCase()));
              return nameMatch || tagMatch;
            });
          }

          if (selectedAnimal) {
            // Animal found - delete directly without confirmation as per requirements
            try {
              const deletedAnimal = await deleteAnimalFromDatabase(
                selectedAnimal.id,
                selectedFarm.id,
                deletionInfo.reason
              );

              const successMessage = language === 'ur' ?
                `✅ جانور کامیابی سے حذف کر دیا گیا:

نام: ${deletedAnimal.name}
نوع: ${deletedAnimal.species}
🆔 ڈیٹابیس ID: ${deletedAnimal.id}
${deletionInfo.reason ? `وجہ: ${deletionInfo.reason}` : ''}

⚠️ یہ عمل واپس نہیں ہو سکتا۔` :
                `✅ Animal Successfully Deleted:

Name: ${deletedAnimal.name}
Species: ${deletedAnimal.species}
🆔 Database ID: ${deletedAnimal.id}
${deletionInfo.reason ? `Reason: ${deletionInfo.reason}` : ''}

⚠️ This action cannot be undone.`;

              return res.json({
                message: successMessage,
                animalData: deletedAnimal,
                deleted: true,
                databaseId: deletedAnimal.id
              });
            } catch (deleteError) {
              console.error('Error deleting animal:', deleteError);
              return res.json({
                message: language === 'ur' ?
                  'جانور حذف کرنے میں خرابی۔ براہ کرم دوبارہ کوشش کریں۔' :
                  'Error deleting animal. Please try again.',
                error: true
              });
            }
          } else {
            // No specific animal mentioned or not found - show visual animal selection list
            if (allAnimals.length === 0) {
              return res.json({
                message: language === 'ur' ?
                  '❌ اس فارم میں کوئی جانور نہیں ہے۔' :
                  '❌ No animals found in this farm.',
                error: true
              });
            }

            const selectionMessage = language === 'ur' ?
              `🗑️ جانور حذف کریں

📋 آپ کے پاس ${allAnimals.length} جانور ${allAnimals.length > 1 ? 'ہیں' : 'ہے'}۔ براہ کرم وہ جانور منتخب کریں جسے آپ حذف کرنا چاہتے ہیں:

⚠️ انتباہ: جانور حذف کرنے سے اس کے تمام ریکارڈز بھی حذف ہو جائیں گے۔
🔒 یہ عمل واپس نہیں ہو سکتا۔` :
              `🗑️ Delete Animal

📋 You have ${allAnimals.length} animal${allAnimals.length > 1 ? 's' : ''} available. Please select the animal you want to delete:

⚠️ Warning: Deleting an animal will also delete all its related records.
🔒 This action cannot be undone.`;

            // Collect all animal images for display
            const animalImages = allAnimals.map(animal => ({
              imageUri: animal.imageUri || null,
              name: animal.name,
              species: animal.species,
              id: animal.id
            }));

            return res.json({
              message: selectionMessage,
              animalImages: animalImages,
              context: {
                action: 'delete_animal',
                farmId: selectedFarm.id,
                farmName: selectedFarm.name,
                reason: deletionInfo.reason,
                needsAnimalSelection: true
              }
            });
          }
        } else {
          // Invalid deletion request
          const invalidMessage = language === 'ur' ?
            `❌ یہ جانور حذف کرنے کی درخواست نہیں لگ رہی۔

🗑️ جانور حذف کرنے کے لیے استعمال کریں:
• "Delete [animal name]"
• "Remove cow Daisy"
• "I sold goat number 123"
• "[animal name] died"
• "جانور حذف کریں"
• "گائے ڈیزی کو ہٹا دیں"

💡 مثال: "Delete my cow Daisy" یا "I sold goat number 45"` :
            `❌ This does not appear to be an animal deletion request.

🗑️ To delete an animal, use commands like:
• "Delete [animal name]"
• "Remove cow Daisy"
• "I sold goat number 123"
• "[animal name] died"
• "Get rid of animal 45"

💡 Example: "Delete my cow Daisy" or "I sold goat number 45"`;

          return res.json({
            message: invalidMessage,
            error: true,
            helpType: 'animal_deletion_syntax'
          });
        }

      } catch (error) {
        console.error('Error processing animal deletion:', error);
        return res.json({
          message: language === 'ur' ?
            'جانور حذف کرنے میں خرابی۔ براہ کرم دوبارہ کوشش کریں۔' :
            'Error processing animal deletion. Please try again.',
          error: true
        });
      }
    }

    // Handle expense deletion prompts
    if (requestType === 'delete_expense') {
      console.log('Processing expense deletion prompt...');

      try {
        // Extract expense deletion information using AI
        const deletionInfo = await analyzePromptWithAI(prompt, 'expense_delete');
        console.log('AI extracted expense deletion info:', deletionInfo);

        // Get context from request body
        const selectedFarmFromDashboard = requestBody.farms;
        let selectedFarm = null;

        if (selectedFarmFromDashboard && selectedFarmFromDashboard.length > 0) {
          selectedFarm = selectedFarmFromDashboard[0];
          console.log('Using farm from dashboard for expense deletion:', selectedFarm.name);
        } else {
          // Fallback: fetch user's farms if no farm data in request
          try {
            const farmsSnapshot = await firestore.collection('farms')
              .where('ownerId', '==', userId)
              .get();

            const userFarms = farmsSnapshot.docs.map(doc => ({
              id: doc.id,
              ...doc.data()
            }));

            selectedFarm = userFarms.find(f => f.name !== 'All farms') || userFarms[0];
            console.log('Using fallback farm for expense deletion:', selectedFarm?.name);
          } catch (error) {
            console.error('Error fetching farms for expense deletion:', error);
          }
        }

        if (!selectedFarm) {
          return res.json({
            message: language === 'ur' ?
              'کوئی فارم دستیاب نہیں۔ پہلے فارم بنائیں۔' :
              'No farms available. Please create a farm first.',
            error: true
          });
        }

        if (deletionInfo && deletionInfo.action === 'delete_expense' && deletionInfo.confidence > 70) {
          // Get all expenses from the farm
          const farmRef = firestore.collection('farms').doc(selectedFarm.id);
          const expensesSnapshot = await farmRef.collection('expenses').get();
          const allExpenses = expensesSnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          }));

          console.log('Found expenses for deletion:', allExpenses.length);

          if (allExpenses.length === 0) {
            const noExpensesMessage = language === 'ur' ?
              `❌ ${selectedFarm.name} میں کوئی اخراجات نہیں ملے۔

💡 پہلے کچھ اخراجات شامل کریں، پھر انہیں حذف کر سکیں گے۔` :
              `❌ No expenses found in ${selectedFarm.name}.

💡 Add some expenses first, then you can delete them.`;

            return res.json({
              message: noExpensesMessage,
              error: true
            });
          }

          // Create visual list of expenses for selection (card-style like animal deletion)
          let expenseImages = [];
          let selectionMessage = '';

          if (language === 'ur') {
            selectionMessage = `🗑️ اخراجات حذف کریں - ${selectedFarm.name}

📋 حذف کرنے کے لیے اخراجات کا انتخاب کریں:`;
          } else {
            selectionMessage = `🗑️ Delete Expense - ${selectedFarm.name}

📋 Select expense to delete:`;
          }

          // Create expense cards for selection (similar to animal cards)
          allExpenses.forEach((expense) => {
            const amount = expense.amount || 0;
            const currency = expense.currency || 'PKR';
            const category = expense.category || 'Unknown';
            const description = expense.description || 'No description';
            const date = expense.date ? new Date(expense.date).toLocaleDateString() : 'Unknown date';
            const paymentMethod = expense.paymentMethod || 'Cash';

            // Create card-style display name
            const cardTitle = `${category}`;
            const cardAmount = `${currency} ${amount.toLocaleString()}`;
            const cardDate = date;
            const cardPayment = paymentMethod;

            // Add expense to selection list (always create cards, with or without receipt images)
            expenseImages.push({
              imageUri: expense.receiptImage || null, // Use receipt image if available, null otherwise
              name: cardTitle,
              amount: cardAmount,
              date: cardDate,
              paymentMethod: cardPayment,
              description: description.length > 50 ? description.substring(0, 50) + '...' : description,
              id: expense.id,
              category: category,
              fullAmount: amount,
              currency: currency
            });
          });

          return res.json({
            message: selectionMessage,
            expenseImages: expenseImages,
            context: {
              action: 'delete_expense',
              farmId: selectedFarm.id,
              farmName: selectedFarm.name,
              reason: deletionInfo.reason,
              needsExpenseSelection: true
            }
          });
        } else {
          // Invalid deletion request
          const invalidMessage = language === 'ur' ?
            `❌ یہ اخراجات حذف کرنے کی درخواست نہیں لگ رہی۔

🗑️ اخراجات حذف کرنے کے لیے استعمال کریں:
• "Delete expense"
• "Remove expense"
• "Remove exp"
• "Vanish expense"
• "Get rid of expense"
• "خرچہ حذف کریں"
• "اخراجات ہٹا دیں"

💡 مثال: "Delete expense" یا "Remove the feed expense"` :
            `❌ This does not appear to be an expense deletion request.

🗑️ To delete an expense, use commands like:
• "Delete expense"
• "Remove expense"
• "Remove exp"
• "Vanish expense"
• "Get rid of expense"
• "Delete the feed expense"

💡 Example: "Delete expense" or "Remove the 5000 PKR expense"`;

          return res.json({
            message: invalidMessage,
            error: true,
            helpType: 'expense_deletion_syntax'
          });
        }

      } catch (error) {
        console.error('Error processing expense deletion:', error);
        return res.json({
          message: language === 'ur' ?
            'اخراجات حذف کرنے میں خرابی۔ براہ کرم دوبارہ کوشش کریں۔' :
            'Error processing expense deletion. Please try again.',
          error: true
        });
      }
    }

    // Handle health record type selection
    if (req.body.context?.needsHealthRecordTypeSelection) {
      console.log('🏥 HEALTH RECORD TYPE SELECTION HANDLER TRIGGERED');
      const userInput = prompt.trim();

      const healthRecordTypes = [
        { id: 'vaccination', label: 'Vaccination', description: 'Immunization records' },
        { id: 'medication', label: 'Medication', description: 'Medicine and treatment records' },
        { id: 'surgery', label: 'Surgery', description: 'Surgical procedures' },
        { id: 'checkup', label: 'Checkup', description: 'General health examinations' },
        { id: 'birth', label: 'Birth', description: 'Birth and delivery records' },
        { id: 'treatment', label: 'Treatment', description: 'General treatments' },
        { id: 'deworming', label: 'Deworming', description: 'Parasite treatment' }
      ];

      let selectedType = null;

      // Try to find by ID first (for clickable selection)
      selectedType = healthRecordTypes.find(type => type.id === userInput);
      if (selectedType) {
        console.log('Selected health record type by ID:', selectedType.label);
      } else {
        // Try to parse as number
        const typeIndex = parseInt(userInput) - 1;
        if (!isNaN(typeIndex) && typeIndex >= 0 && typeIndex < healthRecordTypes.length) {
          selectedType = healthRecordTypes[typeIndex];
          console.log('Selected health record type by index:', selectedType.label);
        } else {
          // Try to find by name
          selectedType = healthRecordTypes.find(type =>
            type.label.toLowerCase().includes(userInput.toLowerCase()) ||
            type.id.toLowerCase().includes(userInput.toLowerCase())
          );
          console.log('Selected health record type by name:', selectedType?.label);
        }
      }

      if (selectedType) {
        // Now proceed with the selected type as if user said "Add [type]"
        const newPrompt = `Add ${selectedType.id}`;

        // Update the request to continue processing
        prompt = newPrompt;
        requestType = 'add_health_record';

        // Continue to the health record handler below
      } else {
        const errorMessage = language === 'ur' ?
          `❌ غلط انتخاب - براہ کرم صحت کے ریکارڈ کی قسم منتخب کریں۔

📋 دستیاب اقسام:
${healthRecordTypes.map((type, index) => `${index + 1}. ${type.label}`).join('\n')}` :
          `❌ Invalid Selection - Please select a health record type.

📋 Available Types:
${healthRecordTypes.map((type, index) => `${index + 1}. ${type.label}`).join('\n')}`;

        return res.json({
          message: errorMessage,
          healthRecordTypeCards: healthRecordTypes.map((type, index) => ({
            id: type.id,
            label: type.label,
            description: type.description,
            index: index + 1
          })),
          context: {
            needsHealthRecordTypeSelection: true
          }
        });
      }
    }

    // Handle health record addition prompts
    if (requestType === 'add_health_record') {

      try {
        // Extract health record information using AI
        const healthRecordInfo = await analyzePromptWithAI(prompt, 'add_health_record');
        console.log('AI extracted health record info:', healthRecordInfo);

        // Check if no record type was specified - show health record type selection
        if (!healthRecordInfo?.recordType || healthRecordInfo.recordType === 'unknown') {
          console.log('No record type specified, showing health record type selection');

          const healthRecordTypes = [
            { id: 'vaccination', label: 'Vaccination', description: 'Immunization records' },
            { id: 'medication', label: 'Medication', description: 'Medicine and treatment records' },
            { id: 'surgery', label: 'Surgery', description: 'Surgical procedures' },
            { id: 'checkup', label: 'Checkup', description: 'General health examinations' },
            { id: 'birth', label: 'Birth', description: 'Birth and delivery records' },
            { id: 'treatment', label: 'Treatment', description: 'General treatments' },
            { id: 'deworming', label: 'Deworming', description: 'Parasite treatment' }
          ];

          const typeSelectionMessage = language === 'ur' ?
            `💡 اوپر سے صحت کے ریکارڈ کی قسم منتخب کریں:

🏥 صحت کا ریکارڈ شامل کریں

📋 دستیاب اقسام:
${healthRecordTypes.map((type, index) => `${index + 1}. ${type.label} - ${type.description}`).join('\n')}` :
            `💡 Select health record type from above:

🏥 Add Health Record

📋 Available Types:
${healthRecordTypes.map((type, index) => `${index + 1}. ${type.label} - ${type.description}`).join('\n')}`;

          return res.json({
            message: typeSelectionMessage,
            healthRecordTypeCards: healthRecordTypes.map((type, index) => ({
              id: type.id,
              label: type.label,
              description: type.description,
              index: index + 1
            })),
            context: {
              needsHealthRecordTypeSelection: true,
              selectedFarm: null // Will be set when type is selected
            }
          });
        }

        // Get context from request body
        const selectedFarmFromDashboard = requestBody.farms;
        let selectedFarm = null;

        if (selectedFarmFromDashboard && selectedFarmFromDashboard.length > 0) {
          selectedFarm = selectedFarmFromDashboard[0];
          console.log('Using farm from dashboard for health record:', selectedFarm.name);
        } else {
          // Fallback: fetch user's farms if no farm data in request
          try {
            const farmsSnapshot = await firestore.collection('farms')
              .where('ownerId', '==', userId)
              .get();

            const userFarms = farmsSnapshot.docs.map(doc => ({
              id: doc.id,
              ...doc.data()
            }));

            selectedFarm = userFarms.find(f => f.name !== 'All farms') || userFarms[0];
            console.log('Using fallback farm for health record:', selectedFarm?.name);
          } catch (farmError) {
            console.error('Error fetching farms for health record:', farmError);
          }
        }

        if (!selectedFarm) {
          return res.json({
            message: language === 'ur' ?
              'کوئی فارم دستیاب نہیں۔ پہلے فارم بنائیں۔' :
              'No farms available. Please create a farm first.',
            error: true
          });
        }

        // Get all animals from the farm
        const farmRef = firestore.collection('farms').doc(selectedFarm.id);
        const animalsSnapshot = await farmRef.collection('animals').get();
        const allAnimals = animalsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));

        console.log('Available animals for health record:', allAnimals.length);

        // Check if animal was mentioned in the prompt
        let selectedAnimal = null;
        if (healthRecordInfo && healthRecordInfo.animalName) {
          selectedAnimal = allAnimals.find(animal =>
            animal.name?.toLowerCase().includes(healthRecordInfo.animalName.toLowerCase())
          );
        }

        // Check if we have record type but no record option - show record options first
        if (healthRecordInfo?.recordType && !healthRecordInfo?.recordOption) {
          console.log('Need to show record options for type:', healthRecordInfo.recordType);

          // Get record options based on type and farm
          try {
            let recordOptions = [];

            // For vaccination and medication, fetch from requests collection
            if (healthRecordInfo.recordType === 'vaccination' || healthRecordInfo.recordType === 'medication') {
              const categoryName = healthRecordInfo.recordType === 'vaccination' ? 'Vaccination' : 'Medication';

              // Fetch from requests collection
              const requestsRef = firestore.collection('farms').doc(selectedFarm.id).collection('requests');
              const requestsQuery = requestsRef
                .where('status', '==', 'approved')
                .where('category', '==', categoryName)
                .where('requestedBy', '==', userId);

              const requestsSnapshot = await requestsQuery.get();

              requestsSnapshot.forEach((doc) => {
                const requestData = doc.data();
                recordOptions.push({
                  id: doc.id,
                  label: requestData.itemName || 'Unknown Item',
                  description: requestData.reason || 'No description available'
                });
              });

              console.log(`Found ${recordOptions.length} ${categoryName} options from requests`);
            } else {
              // For other types, use static options or fetch from lookups
              const categoryMap = {
                'treatment': 'treatmentCow',
                'deworming': 'dewormingCow',
                'surgery': 'surgeryCow',
                'checkup': 'checkupCow',
                'birth': 'birthCow'
              };

              const categoryName = categoryMap[healthRecordInfo.recordType];
              if (categoryName) {
                // You can implement lookup fetching here if needed
                // For now, provide some default options
                const defaultOptions = {
                  'treatment': [
                    { id: 'antibiotics', label: 'Antibiotics', description: 'For bacterial infections' },
                    { id: 'pain-relievers', label: 'Pain Relievers', description: 'NSAIDs for pain and inflammation' },
                    { id: 'anti-inflammatory', label: 'Anti-inflammatory', description: 'For reducing inflammation' },
                    { id: 'wound-care', label: 'Wound Care', description: 'Topical treatments for wounds' }
                  ],
                  'deworming': [
                    { id: 'anthelmintics', label: 'Anthelmintics', description: 'Broad spectrum dewormer' },
                    { id: 'ivermectin', label: 'Ivermectin', description: 'For internal and external parasites' },
                    { id: 'albendazole', label: 'Albendazole', description: 'For roundworms and tapeworms' },
                    { id: 'fenbendazole', label: 'Fenbendazole', description: 'For gastrointestinal parasites' }
                  ],
                  'surgery': [
                    { id: 'castration', label: 'Castration', description: 'Surgical removal of reproductive organs' },
                    { id: 'dehorning', label: 'Dehorning', description: 'Removal of horns' },
                    { id: 'wound-repair', label: 'Wound Repair', description: 'Surgical repair of wounds' },
                    { id: 'cesarean', label: 'Cesarean Section', description: 'Surgical delivery of offspring' },
                    { id: 'other-surgery', label: 'Other Surgery', description: 'Other surgical procedures' }
                  ],
                  'checkup': [
                    { id: 'general-health', label: 'General Health Check', description: 'Routine health examination' },
                    { id: 'pre-breeding', label: 'Pre-breeding Check', description: 'Health check before breeding' },
                    { id: 'post-treatment', label: 'Post-treatment Follow-up', description: 'Follow-up after treatment' },
                    { id: 'pregnancy-check', label: 'Pregnancy Check', description: 'Examination for pregnancy' },
                    { id: 'dental-check', label: 'Dental Examination', description: 'Dental health check' }
                  ],
                  'birth': [
                    { id: 'normal-birth', label: 'Normal Birth', description: 'Natural delivery without complications' },
                    { id: 'assisted-birth', label: 'Assisted Birth', description: 'Birth with assistance' },
                    { id: 'cesarean-birth', label: 'Cesarean Birth', description: 'Surgical delivery' },
                    { id: 'stillbirth', label: 'Stillbirth', description: 'Birth of deceased offspring' },
                    { id: 'multiple-birth', label: 'Multiple Birth', description: 'Birth of twins or more' }
                  ]
                };

                recordOptions = defaultOptions[healthRecordInfo.recordType] || [];
              }
            }

            if (recordOptions.length > 0) {
              const optionsMessage = language === 'ur' ?
                `💡 اوپر سے ${healthRecordInfo.recordType} کا آپشن منتخب کریں:

🏥 ${healthRecordInfo.recordType} کے لیے آپشن منتخب کریں:

📋 دستیاب آپشنز:
${recordOptions.map((option, index) => `${index + 1}. ${option.label} - ${option.description}`).join('\n')}` :
                `💡 Select ${healthRecordInfo.recordType} option from above:

🏥 Select ${healthRecordInfo.recordType} option:

📋 Available Options:
${recordOptions.map((option, index) => `${index + 1}. ${option.label} - ${option.description}`).join('\n')}`;

              // Create option cards for selection interface
              const optionCards = recordOptions.map((option, index) => ({
                id: option.id,
                label: option.label,
                description: option.description,
                index: index + 1
              }));

              return res.json({
                message: optionsMessage,
                optionCards: optionCards,
                context: {
                  healthRecordData: healthRecordInfo,
                  selectedFarm,
                  availableAnimals: allAnimals,
                  recordOptions: recordOptions,
                  needsRecordOptionSelection: true
                }
              });
            } else {
              // No options available for vaccination/medication - stop and inform user
              if (healthRecordInfo.recordType === 'vaccination' || healthRecordInfo.recordType === 'medication') {
                const noOptionsMessage = language === 'ur' ?
                  `❌ ${healthRecordInfo.recordType} کے لیے کوئی آپشن دستیاب نہیں

🏥 ${healthRecordInfo.recordType === 'vaccination' ? 'ویکسینیشن' : 'دوائی'} کے ریکارڈ شامل کرنے کے لیے:

1️⃣ پہلے Records → Add سکرین سے ${healthRecordInfo.recordType === 'vaccination' ? 'ویکسین' : 'دوائی'} کی درخواست کریں
2️⃣ منظوری کے بعد یہاں چیٹ میں ریکارڈ شامل کر سکیں گے

💡 یا آپ Treatment یا Deworming ریکارڈ شامل کر سکتے ہیں جن کے لیے پہلے سے آپشنز موجود ہیں۔` :
                  `❌ No ${healthRecordInfo.recordType} options available

🏥 To add ${healthRecordInfo.recordType} records:

1️⃣ First request ${healthRecordInfo.recordType === 'vaccination' ? 'vaccines' : 'medications'} through Records → Add screen
2️⃣ After approval, you can add records here in chat

💡 Or you can add Treatment or Deworming records which have pre-available options.`;

                return res.json({
                  message: noOptionsMessage,
                  error: true
                });
              } else {
                // For other types, proceed with animal selection
                console.log('No record options available, proceeding to animal selection');
              }
            }
          } catch (error) {
            console.error('Error fetching record options:', error);
            // Continue to animal selection if options fetch fails
          }
        }

        // If no animal found or no animal mentioned, show animal selection
        if (!selectedAnimal && allAnimals.length > 0) {
          const selectionMessage = language === 'ur' ?
            `🏥 صحت کا ریکارڈ شامل کریں

📋 تجزیہ شدہ معلومات:
• قسم: ${healthRecordInfo?.recordType || 'نامعلوم'}
• تفصیل: ${healthRecordInfo?.recordOption || 'نامعلوم'}
• ڈاکٹر: ${healthRecordInfo?.practitioner || 'خود'}
• تاریخ: ${healthRecordInfo?.date || 'آج'}
• نوٹس: ${healthRecordInfo?.notes || 'کوئی نوٹس نہیں'}

🐄 جانور کا انتخاب کریں:` :
            `🏥 Add Health Record

📋 Extracted Information:
• Type: ${healthRecordInfo?.recordType || 'Unknown'}
• Details: ${healthRecordInfo?.recordOption || 'Unknown'}
• Practitioner: ${healthRecordInfo?.practitioner || 'Self'}
• Date: ${healthRecordInfo?.date || 'Today'}
• Notes: ${healthRecordInfo?.notes || 'No notes'}

🐄 Select Animal:`;

          // Collect all animal images for display
          const animalImages = allAnimals
            .map(animal => ({
              imageUri: animal.imageUri,
              name: animal.name,
              species: animal.species,
              id: animal.id
            }));

          return res.json({
            message: selectionMessage,
            animalImages: animalImages,
            context: {
              healthRecordData: healthRecordInfo,
              selectedFarm,
              availableAnimals: allAnimals,
              needsAnimalSelection: true
            }
          });
        }

        if (allAnimals.length === 0) {
          return res.json({
            message: language === 'ur' ?
              '❌ اس فارم میں کوئی جانور نہیں ملا۔ پہلے جانور شامل کریں۔' :
              '❌ No animals found in this farm. Please add animals first.',
            error: true
          });
        }

        // If we have a selected animal, proceed with record creation
        if (selectedAnimal) {
          // Validate required fields
          if (!healthRecordInfo?.recordType) {
            return res.json({
              message: language === 'ur' ?
                '❌ صحت کے ریکارڈ کی قسم درکار ہے (جیسے vaccination, medication, surgery, checkup, birth)۔' :
                '❌ Health record type is required (e.g., vaccination, medication, surgery, checkup, birth).',
              error: true
            });
          }

          // Create health record data
          const currentDate = new Date();
          const recordData = {
            farmId: selectedFarm.id,
            farmName: selectedFarm.name,
            animalId: selectedAnimal.id,
            animalName: selectedAnimal.name,
            recordType: healthRecordInfo.recordType,
            recordOption: healthRecordInfo.recordOption || 'General treatment',
            practitioner: healthRecordInfo.practitioner || 'self',
            notes: healthRecordInfo.notes || `${healthRecordInfo.recordType} administered to ${selectedAnimal.name}`,
            date: healthRecordInfo.date ? new Date(healthRecordInfo.date).toISOString() : currentDate.toISOString(),
            createdBy: userId,
            createdAt: currentDate.toISOString(),
            updatedAt: currentDate.toISOString()
          };

          console.log('Creating health record:', recordData);

          // Save to database (you'll need to implement this function)
          // For now, we'll just return the structured data
          const successMessage = language === 'ur' ?
            `✅ صحت کا ریکارڈ تیار ہے!

🏡 فارم: ${recordData.farmName}
🐄 جانور: ${recordData.animalName}
📋 قسم: ${recordData.recordType}
💊 تفصیل: ${recordData.recordOption}
👨‍⚕️ ڈاکٹر: ${recordData.practitioner}
📅 تاریخ: ${new Date(recordData.date).toLocaleDateString()}
📝 نوٹس: ${recordData.notes}

💾 محفوظ کرنے کے لیے "ہاں" ٹائپ کریں۔` :
            `✅ Health Record Ready!

🏡 Farm: ${recordData.farmName}
🐄 Animal: ${recordData.animalName}
📋 Type: ${recordData.recordType}
💊 Details: ${recordData.recordOption}
👨‍⚕️ Practitioner: ${recordData.practitioner}
📅 Date: ${new Date(recordData.date).toLocaleDateString()}
📝 Notes: ${recordData.notes}

💾 Type "Yes" to save this record.`;

          return res.json({
            message: successMessage,
            healthRecordData: recordData,
            context: {
              healthRecordData: recordData,
              selectedFarm,
              selectedAnimal,
              readyToSave: true
            }
          });
        }

      } catch (error) {
        console.error('Error processing health record prompt:', error);
        return res.json({
          message: language === 'ur' ?
            'صحت کے ریکارڈ کی معلومات پروسیس کرنے میں خرابی۔ براہ کرم دوبارہ کوشش کریں۔' :
            'Error processing health record information. Please try again.',
          error: true
        });
      }
    }

    // Handle "yes" response for saving health check
    const lowerMessage = prompt.toLowerCase();
    // Handle "yes" response for saving health record
    if (lowerMessage.includes('yes') && req.body.context?.healthRecordData && req.body.context?.readyToSave) {
      const { healthRecordData, selectedFarm, selectedAnimal } = req.body.context;

      console.log('Processing yes response for health record save:', {
        hasHealthRecordData: !!healthRecordData,
        hasSelectedFarm: !!selectedFarm,
        hasSelectedAnimal: !!selectedAnimal,
        recordType: healthRecordData?.recordType
      });

      try {
        // Ensure we have the required data
        if (!selectedFarm || !selectedAnimal || !healthRecordData) {
          throw new Error('Missing farm, animal, or health record information');
        }

        // Create the health record data for Firestore
        const currentDate = new Date();
        const recordData = {
          animalId: selectedAnimal.id,
          date: healthRecordData.date ? new Date(healthRecordData.date).getTime() : currentDate.getTime(),
          type: healthRecordData.recordType,
          title: `${healthRecordData.recordType}: ${healthRecordData.recordOption || 'General treatment'}`,
          description: healthRecordData.notes || `${healthRecordData.recordType} administered to ${selectedAnimal.name}`,
          symptoms: [],
          practitioner: healthRecordData.practitioner || 'self',
          recordOption: healthRecordData.recordOption || 'General treatment',
          createdBy: userId,
          createdAt: currentDate.getTime(),
          updatedAt: currentDate.getTime()
        };

        // Add practitioner information to description
        if (healthRecordData.practitioner && healthRecordData.practitioner !== 'self') {
          recordData.description += `\n\nPractitioner: ${healthRecordData.practitioner}`;
        }

        // Add record option details to description
        if (healthRecordData.recordOption) {
          recordData.description += `\n\nDetails: ${healthRecordData.recordOption}`;
        }

        console.log('Saving health record to Firestore:', {
          farmId: selectedFarm.id,
          animalId: selectedAnimal.id,
          recordType: recordData.type
        });

        // Save to the animal's records subcollection
        const farmRef = firestore.collection('farms').doc(selectedFarm.id);
        const animalRef = farmRef.collection('animals').doc(selectedAnimal.id);
        const recordRef = await animalRef.collection('records').add(recordData);

        const savedRecord = {
          id: recordRef.id,
          animalId: selectedAnimal.id,
          farmId: selectedFarm.id,
          ...recordData
        };

        console.log('Health record saved successfully:', savedRecord.id);

        const confirmationMessage = language === 'ur' ?
          `✅ صحت کا ریکارڈ محفوظ ہو گیا!

🏥 تفصیلات:
• فارم: ${selectedFarm?.name || 'نامعلوم فارم'}
• جانور: ${selectedAnimal?.name || 'نامعلوم جانور'}
• قسم: ${recordData.type}
• تفصیل: ${recordData.recordOption}
• ڈاکٹر: ${recordData.practitioner}
• تاریخ: ${new Date(recordData.date).toLocaleDateString('ur-PK')}

📋 ID: ${savedRecord.id}

💾 ڈیٹا کامیابی سے محفوظ ہو گیا ہے۔

🔄 اگلے قدم: آپ مزید صحت کے ریکارڈز شامل کر سکتے ہیں یا دوسرے جانوروں کا علاج ریکارڈ کر سکتے ہیں۔` :
          `✅ Health Record Saved Successfully!

🏥 Details:
• Farm: ${selectedFarm?.name || 'Unknown Farm'}
• Animal: ${selectedAnimal?.name || 'Unknown Animal'}
• Type: ${recordData.type}
• Details: ${recordData.recordOption}
• Practitioner: ${recordData.practitioner}
• Date: ${new Date(recordData.date).toLocaleDateString()}

📋 ID: ${savedRecord.id}

💾 Data has been successfully saved to your records.

🔄 Next Steps: You can add more health records or record treatments for other animals.`;

        return res.json({
          message: confirmationMessage,
          healthRecordSaved: true,
          savedData: savedRecord
        });

      } catch (error) {
        console.error('Error saving health record:', error);
        const errorMessage = language === 'ur' ?
          '❌ صحت کا ریکارڈ محفوظ کرنے میں خرابی ہوئی۔ براہ کرم دوبارہ کوشش کریں۔' :
          '❌ Failed to save health record. Please try again.';

        return res.json({
          message: errorMessage,
          error: true
        });
      }
    }

    // Handle record option selection for health records
    if (req.body.context?.needsRecordOptionSelection && req.body.context?.recordOptions && req.body.context?.healthRecordData) {
      console.log('🏥 HEALTH RECORD OPTION SELECTION HANDLER TRIGGERED');
      const { recordOptions, healthRecordData, selectedFarm, availableAnimals } = req.body.context;

      console.log('Processing record option selection:', {
        prompt,
        recordOptionsCount: recordOptions.length,
        recordType: healthRecordData?.recordType
      });

      // Check if user provided an option number or name
      const selection = prompt.trim();
      let selectedOption = null;

      // Try to parse as number first
      const optionIndex = parseInt(selection) - 1;
      if (!isNaN(optionIndex) && optionIndex >= 0 && optionIndex < recordOptions.length) {
        selectedOption = recordOptions[optionIndex];
        console.log('Selected option by index:', selectedOption.label);
      } else {
        // Try to find by name/label
        selectedOption = recordOptions.find(option =>
          option.label.toLowerCase().includes(selection.toLowerCase())
        );
        console.log('Selected option by name:', selectedOption?.label);
      }

      if (selectedOption) {
        // Update health record data with selected option
        const updatedHealthRecordData = {
          ...healthRecordData,
          recordOption: selectedOption.label,
          recordOptionId: selectedOption.id
        };

        // Now proceed to animal selection
        const animalSelectionMessage = language === 'ur' ?
          `✅ آپشن منتخب کیا گیا: ${selectedOption.label}

🏥 صحت کا ریکارڈ:
• قسم: ${updatedHealthRecordData.recordType}
• تفصیل: ${selectedOption.label}
• تفصیلات: ${selectedOption.description}

🐄 اب جانور کا انتخاب کریں:` :
          `✅ Option Selected: ${selectedOption.label}

🏥 Health Record:
• Type: ${updatedHealthRecordData.recordType}
• Details: ${selectedOption.label}
• Description: ${selectedOption.description}

🐄 Now select an animal:`;

        // Collect all animal images for display
        const animalImages = availableAnimals
          .map(animal => ({
            imageUri: animal.imageUri,
            name: animal.name,
            species: animal.species,
            id: animal.id
          }));

        console.log('🏥 SETTING CONTEXT FOR ANIMAL SELECTION:', {
          hasHealthRecordData: !!updatedHealthRecordData,
          recordType: updatedHealthRecordData?.recordType,
          recordOption: updatedHealthRecordData?.recordOption,
          hasSelectedFarm: !!selectedFarm,
          availableAnimalsCount: availableAnimals?.length,
          needsAnimalSelection: true,
          needsRecordOptionSelection: false
        });

        return res.json({
          message: animalSelectionMessage,
          animalImages: animalImages,
          context: {
            healthRecordData: updatedHealthRecordData,
            selectedFarm,
            availableAnimals: availableAnimals,
            needsAnimalSelection: true,
            needsRecordOptionSelection: false
          }
        });
      } else {
        const errorMessage = language === 'ur' ?
          `❌ غلط انتخاب - براہ کرم صحیح نمبر یا آپشن کا نام ٹائپ کریں۔

📋 دستیاب آپشنز:
${recordOptions.map((option, index) => `${index + 1}. ${option.label}`).join('\n')}` :
          `❌ Invalid Selection - Please type the correct number or option name.

📋 Available Options:
${recordOptions.map((option, index) => `${index + 1}. ${option.label}`).join('\n')}`;

        return res.json({
          message: errorMessage,
          context: req.body.context
        });
      }
    }

    // Handle animal selection for health records

    if (req.body.context?.needsAnimalSelection && req.body.context?.healthRecordData && req.body.context?.availableAnimals) {
      console.log('🏥 HEALTH RECORD ANIMAL SELECTION HANDLER TRIGGERED');
      const { availableAnimals, healthRecordData, selectedFarm } = req.body.context;

      console.log('Processing animal selection for health record:', {
        prompt,
        promptLength: prompt?.length,
        availableAnimalsCount: availableAnimals.length,
        recordType: healthRecordData?.recordType,
        hasSelectedFarm: !!selectedFarm,
        contextKeys: Object.keys(req.body.context)
      });

      // Check if user provided an animal ID, number, or animal name
      const selection = prompt.trim();
      let selectedAnimal = null;

      // Try to find by ID first (for clickable selection)
      selectedAnimal = availableAnimals.find(animal => animal.id === selection);
      if (selectedAnimal) {
        console.log('Selected animal by ID:', selectedAnimal.name);
      } else {
        // Try to parse as number
        const animalIndex = parseInt(selection) - 1;
        if (!isNaN(animalIndex) && animalIndex >= 0 && animalIndex < availableAnimals.length) {
          selectedAnimal = availableAnimals[animalIndex];
          console.log('Selected animal by index:', selectedAnimal.name);
        } else {
          // Try to find by name
          selectedAnimal = availableAnimals.find(animal =>
            animal.name.toLowerCase().includes(selection.toLowerCase())
          );
          console.log('Selected animal by name:', selectedAnimal?.name);
        }
      }

      if (selectedAnimal) {
        // Create complete health record data
        const currentDate = new Date();
        const completeRecordData = {
          ...healthRecordData,
          farmId: selectedFarm.id,
          farmName: selectedFarm.name,
          animalId: selectedAnimal.id,
          animalName: selectedAnimal.name,
          date: healthRecordData.date ? new Date(healthRecordData.date).toISOString() : currentDate.toISOString(),
          createdBy: userId,
          createdAt: currentDate.toISOString(),
          updatedAt: currentDate.toISOString()
        };

        const confirmationMessage = language === 'ur' ?
          `✅ جانور منتخب کیا گیا: ${selectedAnimal.name}

🏥 صحت کا ریکارڈ تیار ہے:
• فارم: ${selectedFarm?.name || 'نامعلوم فارم'}
• جانور: ${selectedAnimal.name} (${selectedAnimal.species})
• قسم: ${completeRecordData.recordType}
• تفصیل: ${completeRecordData.recordOption || 'عام علاج'}
• ڈاکٹر: ${completeRecordData.practitioner || 'خود'}
• تاریخ: ${new Date(completeRecordData.date).toLocaleDateString()}
• نوٹس: ${completeRecordData.notes || 'کوئی نوٹس نہیں'}

💾 محفوظ کرنے کے لیے "ہاں" ٹائپ کریں۔` :
          `✅ Animal Selected: ${selectedAnimal.name}

🏥 Health Record Ready:
• Farm: ${selectedFarm?.name || 'Unknown Farm'}
• Animal: ${selectedAnimal.name} (${selectedAnimal.species})
• Type: ${completeRecordData.recordType}
• Details: ${completeRecordData.recordOption || 'General treatment'}
• Practitioner: ${completeRecordData.practitioner || 'Self'}
• Date: ${new Date(completeRecordData.date).toLocaleDateString()}
• Notes: ${completeRecordData.notes || 'No notes'}

💾 Type "Yes" to save this record.`;

        return res.json({
          message: confirmationMessage,
          image: selectedAnimal.imageUri || undefined,
          animalInfo: {
            name: selectedAnimal.name,
            species: selectedAnimal.species,
            id: selectedAnimal.id
          },
          context: {
            healthRecordData: completeRecordData,
            selectedFarm,
            selectedAnimal,
            readyToSave: true,
            needsAnimalSelection: false
          }
        });
      } else {
        const errorMessage = language === 'ur' ?
          `❌ غلط انتخاب - براہ کرم کوئی جانور منتخب کریں۔

🏥 صحت کا ریکارڈ:
• قسم: ${healthRecordData?.recordType || 'نامعلوم'}
• تفصیل: ${healthRecordData?.recordOption || 'نامعلوم'}

🐄 جانور کا نام یا نمبر ٹائپ کریں:` :
          `❌ Invalid Selection - Please select an animal.

🏥 Health Record:
• Type: ${healthRecordData?.recordType || 'Unknown'}
• Details: ${healthRecordData?.recordOption || 'Unknown'}

🐄 Please select an animal with Animal Name:`;

        // Collect all animal images to show in the error message
        const animalImages = availableAnimals
          .map(animal => ({
            imageUri: animal.imageUri,
            name: animal.name,
            species: animal.species,
            id: animal.id
          }));

        return res.json({
          message: errorMessage,
          animalImages: animalImages,
          context: {
            healthRecordData,
            selectedFarm,
            availableAnimals,
            needsAnimalSelection: true
          }
        });
      }
    }

    // Handle animal ID selection for health records (fallback handler)
    // This handles cases where animal ID is sent but context flags might not be set properly
    if (req.body.context?.healthRecordData && !req.body.context?.selectedAnimal && prompt && prompt.length > 10 && prompt.includes('-')) {
      const { healthRecordData, selectedFarm, availableAnimals } = req.body.context;

      console.log('🔍 FALLBACK HANDLER CONDITIONS:', {
        hasHealthRecordData: !!healthRecordData,
        hasSelectedFarm: !!selectedFarm,
        hasAvailableAnimals: !!availableAnimals,
        availableAnimalsCount: availableAnimals?.length,
        promptLength: prompt?.length,
        prompt: prompt?.substring(0, 20)
      });

      if (availableAnimals && availableAnimals.length > 0) {
        // Try to find the animal by ID
        const selectedAnimal = availableAnimals.find(animal => animal.id === prompt.trim());

        if (selectedAnimal) {
          console.log('🏥 ANIMAL FOUND BY ID FOR HEALTH RECORD:', selectedAnimal.name);

          // Create complete health record data
          const currentDate = new Date();
          const completeRecordData = {
            ...healthRecordData,
            farmId: selectedFarm.id,
            farmName: selectedFarm.name,
            animalId: selectedAnimal.id,
            animalName: selectedAnimal.name,
            date: healthRecordData.date ? new Date(healthRecordData.date).toISOString() : currentDate.toISOString(),
            createdBy: userId,
            createdAt: currentDate.toISOString(),
            updatedAt: currentDate.toISOString()
          };

          const confirmationMessage = language === 'ur' ?
            `✅ جانور منتخب کیا گیا: ${selectedAnimal.name}

🏥 صحت کا ریکارڈ تیار ہے:
• فارم: ${selectedFarm?.name || 'نامعلوم فارم'}
• جانور: ${selectedAnimal.name} (${selectedAnimal.species})
• قسم: ${completeRecordData.recordType}
• تفصیل: ${completeRecordData.recordOption || 'عام علاج'}
• ڈاکٹر: ${completeRecordData.practitioner || 'خود'}
• تاریخ: ${new Date(completeRecordData.date).toLocaleDateString()}
• نوٹس: ${completeRecordData.notes || 'کوئی نوٹس نہیں'}

📋 کیا آپ یہ صحت کا ریکارڈ محفوظ کرنا چاہتے ہیں؟ تصدیق کے لیے "ہاں" ٹائپ کریں۔` :
            `✅ Animal Selected: ${selectedAnimal.name}

🏥 Health Record Ready:
• Farm: ${selectedFarm?.name || 'Unknown Farm'}
• Animal: ${selectedAnimal.name} (${selectedAnimal.species})
• Type: ${completeRecordData.recordType}
• Details: ${completeRecordData.recordOption || 'General treatment'}
• Practitioner: ${completeRecordData.practitioner || 'Self'}
• Date: ${new Date(completeRecordData.date).toLocaleDateString()}
• Notes: ${completeRecordData.notes || 'No notes'}

📋 Would you like to save this health record? Type "Yes" to confirm.`;

          return res.json({
            message: confirmationMessage,
            image: selectedAnimal.imageUri || undefined,
            animalInfo: {
              name: selectedAnimal.name,
              species: selectedAnimal.species,
              id: selectedAnimal.id
            },
            context: {
              healthRecordData: completeRecordData,
              selectedFarm,
              selectedAnimal,
              readyToSave: true,
              needsAnimalSelection: false
            }
          });
        }
      }
    }

    // Handle "yes" response for health record (fallback - when context is partially lost)
    if (lowerMessage.includes('yes') && req.body.context?.selectedAnimal && req.body.context?.selectedFarm && !req.body.context?.healthRecordData && !req.body.context?.animalData && !req.body.context?.milkingData && !req.body.context?.healthData) {
      console.log('🏥 FALLBACK YES HANDLER FOR HEALTH RECORD DETECTED');
      console.log('Context has selectedAnimal and selectedFarm but missing healthRecordData - this might be a health record with lost context');

      // This is likely a health record "yes" response where context was partially lost
      // We should inform the user that the context was lost and ask them to try again
      const errorMessage = language === 'ur' ?
        `❌ معذرت، ڈیٹا محفوظ کرنے میں خرابی ہوئی۔

🔄 براہ کرم دوبارہ کوشش کریں:
• صحت کا ریکارڈ شامل کرنے کے لیے "Record [type]" ٹائپ کریں
• جہاں [type] = vaccination, medication, surgery, checkup, یا birth

💡 مثال: "Record birth" یا "Record surgery"` :
        `❌ Sorry, there was an error saving the data.

🔄 Please try again:
• To add a health record, type "Record [type]"
• Where [type] = vaccination, medication, surgery, checkup, or birth

💡 Example: "Record birth" or "Record surgery"`;

      return res.json({
        message: errorMessage,
        error: true
      });
    }

    // Handle "yes" response for saving new animal
    if (lowerMessage.includes('yes') && req.body.context?.animalData && req.body.context?.readyToSave) {
      const { animalData, imageUri } = req.body.context;

      try {
        // Get selected farm from request body (from dashboard)
        const selectedFarmFromDashboard = requestBody.farms;
        let selectedFarm = null;

        if (selectedFarmFromDashboard && selectedFarmFromDashboard.length > 0) {
          // Use the first farm from the dashboard selection
          selectedFarm = selectedFarmFromDashboard[0];
          console.log('Using farm from dashboard for animal:', selectedFarm.name);
        } else {
          // Fallback: fetch user's farms if no farm data in request
          try {
            const farmsSnapshot = await firestore.collection('farms')
              .where('createdBy', '==', userId)
              .limit(1)
              .get();

            if (!farmsSnapshot.empty) {
              const farmDoc = farmsSnapshot.docs[0];
              selectedFarm = {
                id: farmDoc.id,
                ...farmDoc.data()
              };
              console.log('Using fallback farm for animal:', selectedFarm.name);
            }
          } catch (farmError) {
            console.error('Error fetching farms for animal:', farmError);
          }
        }

        if (!selectedFarm) {
          return res.json({
            message: language === 'ur' ?
              '❌  خرابی : پہلے فارم بنائیں۔' :
              '❌  Error : Please create a farm first.',
          });
        }

        const farmRef = firestore.collection('farms').doc(selectedFarm.id);

        // Prepare animal data for Firestore
        const newAnimalData = {
          ...animalData,
          imageUri: imageUri,
          farmId: selectedFarm.id,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          createdBy: userId,
          status: 'active'
        };

        // Add animal to the farm's animals subcollection
        const animalRef = await farmRef.collection('animals').add(newAnimalData);

        console.log('Animal saved successfully:', animalRef.id);

        const successMessage = language === 'ur' ?
          `✅  جانور کامیابی سے شامل کر دیا گیا! 

🐄  ${animalData.species}  آپ کے فارم میں شامل کر دیا گیا ہے۔
📋  ID : ${animalRef.id}

🎉 اب آپ اس جانور کی صحت کی جانچ کر سکتے ہیں!` :
          `✅  Animal Added Successfully! 

🐄  ${animalData.species}  has been added to your farm.
📋  ID : ${animalRef.id}

🎉 You can now perform health checks on this animal!`;

        return res.json({
          message: successMessage,
          animalSaved: true,
          animalId: animalRef.id
        });

      } catch (error) {
        console.error('Error saving animal:', error);
        return res.json({
          message: language === 'ur' ?
            '❌  خرابی : جانور محفوظ نہیں ہو سکا۔' :
            '❌  Error : Failed to save animal.',
        });
      }
    }

    // Handle "yes" response for saving health check
    if (lowerMessage.includes('yes') && req.body.context?.healthData && req.body.context?.selectedAnimal) {
      const { healthData, selectedFarm, selectedAnimal } = req.body.context;

      try {
        // Ensure we have the required data
        if (!selectedFarm || !selectedAnimal) {
          throw new Error('Missing farm or animal information');
        }

        // Save the health check data to Firestore
        const { imageContent, recommendations, respiration, ...filteredHealthAssessment } = healthData.healthAssessment;

        const healthCheckData = {
          ...filteredHealthAssessment,
          notes: "The fecal matter appears well-formed, indicating normal digestive function. No signs of dehydration or digestive issues are present.",
          imageUri: imageUri || "https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/healthChecks%2Fz1eV7f0ddtETbQ8UWGqK%2F1751610897548?alt=media&token=500bb387-6a10-4449-a2c2-c02e654367f1",
          nextCheckDate: Date.now() + (7 * 24 * 60 * 60 * 1000), // 7 days from now
          respiration: "normal",
          abnormalities: healthData.abnormalities,
          date: new Date().toISOString(),
          createdAt: new Date().toISOString(),
          updatedAt: Date.now(),
          createdBy: userId
        };

        // Save to the animal's healthChecks subcollection
        const farmRef = firestore.collection('farms').doc(selectedFarm.id);
        const animalRef = farmRef.collection('animals').doc(selectedAnimal.id);
        const healthCheckRef = await animalRef.collection('healthChecks').add(healthCheckData);

        const savedHealthCheck = {
          id: healthCheckRef.id,
          animalId: selectedAnimal.id,
          farmId: selectedFarm.id,
          ...healthCheckData
        };

        console.log('Health check saved successfully:', savedHealthCheck.id);

        // Save the selected animal to session for future health checks
        if (userId && selectedAnimal) {
          setSelectedAnimal(userId, selectedAnimal);
          console.log('💾 SAVED ANIMAL TO SESSION after successful health check:', selectedAnimal.name);
        }

        const confirmationMessage = language === 'ur' ?
          `✅  صحت کی جانچ محفوظ ہو گئی! 

🏥  تفصیلات :
•  فارم : ${selectedFarm?.name || 'نامعلوم فارم'}
•  جانور : ${selectedAnimal?.name || 'نامعلوم جانور'}
•  تاریخ : ${new Date().toLocaleDateString('ur-PK')}
•  تصویر کا مواد : ${healthData.imageContent}

📊  صحت کی صورتحال : ${healthData.abnormalities ? '⚠️ غیر معمولی' : '✅ نارمل'}

💾  ڈیٹا کامیابی سے محفوظ ہو گیا ہے۔ 

🔄  اگلے قدم : آپ مزید صحت کی جانچ کر سکتے ہیں یا دوسرے جانوروں کا معائنہ کر سکتے ہیں۔` :
          `✅  Health Check Saved Successfully! 

🏥  Details :
•  Farm : ${selectedFarm?.name || 'Unknown Farm'}
•  Animal : ${selectedAnimal?.name || 'Unknown Animal'}
•  Date : ${new Date().toLocaleDateString()}
•  Image Content : ${healthData.imageContent}

📊  Health Status : ${healthData.abnormalities ? '⚠️ Abnormal' : '✅ Normal'}

💾  Data has been successfully saved to your records. 

🔄  Next Steps : You can perform more health checks or examine other animals.`;

        return res.json({
          message: confirmationMessage,
          healthCheckSaved: true,
          savedData: savedHealthCheck
        });
      } catch (error) {
        const errorMessage = language === 'ur' ?
          '❌ صحت کی جانچ محفوظ کرنے میں خرابی ہوئی۔ براہ کرم دوبارہ کوشش کریں۔' :
          '❌ Failed to save health check. Please try again.';

        return res.json({ message: errorMessage });
      }
    }

    // Handle "yes" response for saving milking record
    if (lowerMessage.includes('yes') && req.body.context?.milkingData && req.body.context?.readyToSave) {
      const { milkingData } = req.body.context;

      try {
        // Save the milking record to Firestore
        const farmRef = firestore.collection('farms').doc(milkingData.farmId);
        const milkingCollectionRef = farmRef.collection('milking');

        // Clean the data by removing undefined values
        const cleanData = {};
        Object.entries(milkingData).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            cleanData[key] = value;
          }
        });

        const milkingRecord = await milkingCollectionRef.add({
          ...cleanData,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });

        // Log activity for the milking record
        try {
          const activitiesRef = farmRef.collection('activities');
          await activitiesRef.add({
            farmId: milkingData.farmId,
            userId: milkingData.createdBy,
            action: 'Milking Record Added',
            description: `Added milking record for ${milkingData.animalName}: ${milkingData.quantity}L (${milkingData.session} session)`,
            createdAt: new Date(),
            updatedAt: new Date(),
            appType: "2"
          });
        } catch (activityError) {
          console.error('Error logging activity for milking record:', activityError);
          // Don't fail the main operation if activity logging fails
        }

        console.log('Milking record saved successfully:', milkingRecord.id);

        const successMessage = language === 'ur' ?
          `✅  دودھ کا ریکارڈ محفوظ ہو گیا! 

🐄  جانور : ${milkingData.animalName} (${milkingData.animalSpecies})
⏰  وقت : ${milkingData.session}
🥛  مقدار : ${milkingData.quantity} لیٹر
⭐  کوالٹی : ${milkingData.quality}
📋  ID : ${milkingRecord.id}

💾  ڈیٹا کامیابی سے محفوظ ہو گیا ہے۔ 

🔄  اگلے قدم : آپ مزید دودھ کے ریکارڈ شامل کر سکتے ہیں۔` :
          `✅  Milking Record Saved Successfully! 

🐄  Animal : ${milkingData.animalName} (${milkingData.animalSpecies})
⏰  Session : ${milkingData.session}
🥛  Quantity : ${milkingData.quantity} liters
⭐  Quality : ${milkingData.quality}
📋  ID : ${milkingRecord.id}

💾  Data has been successfully saved to your records. 

🔄  Next Steps : You can add more milking records or check milking statistics.`;

        return res.json({
          message: successMessage,
          milkingSaved: true,
          milkingId: milkingRecord.id
        });

      } catch (error) {
        console.error('Error saving milking record:', error);
        const errorMessage = language === 'ur' ?
          '❌ دودھ کا ریکارڈ محفوظ کرنے میں خرابی ہوئی۔ براہ کرم دوبارہ کوشش کریں۔' :
          '❌ Failed to save milking record. Please try again.';

        return res.json({
          message: errorMessage,
          error: true
        });
      }
    }

    // Regular chat functionality
    const systemMessage = language === 'ur'
      ? 'آپ ایک مددگار اسسٹنٹ ہیں۔ اردو میں جواب دیں۔'
      : 'You are a helpful assistant. Respond in English.';

    messages.push({ role: 'system', content: systemMessage });
    messages.push({ role: 'user', content: prompt });

    const completion = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: messages,
    });

    const responseMessage = completion.choices[0]?.message?.content || 'No response generated';
    console.log('Chat response:', responseMessage);

    res.json({
      message: responseMessage,
    });

  } catch (error) {
    console.error('=== SERVER ERROR ===');
    console.error('Error details:', error);
    console.error('Stack trace:', error.stack);

    res.status(500).json({
      error: 'Failed to process chat request',
      details: error.message,
      timestamp: new Date().toISOString(),
      message: req.body.language === 'ur' ?
        'معذرت، کچھ غلط ہوا۔ براہ کرم دوبارہ کوشش کریں۔' :
        'Sorry, something went wrong. Please try again.'
    });
  }
};
